import { useEffect, useState } from 'react'

import { formatEther } from 'viem'
import { useReadContract } from 'wagmi'

import useGetSolanaMetadata from '../pump404/useGetSolanaMetadata'
import { isSolanaChain } from '@/hooks/useSolanaInfo'
import { contractConfigList } from '@/libs/contract/contractConfig'
import { IAddress } from '@/types/IAddress'
import { IChain } from '@/types/IChain'

const useRoyaltyInfo = (tokenId: string, salePrice: string = '0', contractAddress: IAddress, chainId: IChain) => {
  const issolanaChain = isSolanaChain(chainId)
  const [royaltyInfoForSolana, setRoyaltyInfoForSolana] = useState<any>({ creator: '', saleRoyaltyPrice: '0' })
  const { getSolanaCreator } = useGetSolanaMetadata(chainId)
  const { data, isLoading, isError, isSuccess } = useReadContract({
    ...contractConfigList['fusion'],
    address: contractAddress || contractConfigList['fusion']?.address,
    functionName: 'royaltyInfo',
    chainId: Number(chainId),
    args: [tokenId, salePrice],
    query: {
      enabled: !!tokenId && !!salePrice && !!contractAddress && !!chainId && !issolanaChain,
      staleTime: 60 * 60 * 24,
    },
  })
  useEffect(() => {
    const getSolanaRoyaltyInfo = async () => {
      if (issolanaChain && contractAddress) {
        const creator = await getSolanaCreator(contractAddress)
        const saleRoyaltyPrice = '0'
        const royaltyInfo = {
          creator,
          saleRoyaltyPrice,
        }
        setRoyaltyInfoForSolana(royaltyInfo)
      } else {
        setRoyaltyInfoForSolana({ creator: '', saleRoyaltyPrice: '0' })
      }
    }
    getSolanaRoyaltyInfo()
  }, [contractAddress])
  const success = !isLoading && isSuccess
  const dataList = data as string[]
  const creator = success ? (dataList?.[0] as IAddress) : '0x'
  const saleRoyaltyPrice = success ? formatEther(dataList?.[1] as unknown as bigint) : '0'
  const royaltyInfo = {
    creator,
    saleRoyaltyPrice,
  }
  return {
    royaltyInfo: issolanaChain ? royaltyInfoForSolana : royaltyInfo,
    isLoading,
    isError,
    isSuccess,
  }
}

export default useRoyaltyInfo
