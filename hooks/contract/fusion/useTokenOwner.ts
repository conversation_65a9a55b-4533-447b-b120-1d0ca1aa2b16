import { useEffect, useMemo, useState } from 'react'

import { useParams } from 'next/navigation'
// import { useQuery } from '@tanstack/react-query'
import { useReadContract } from 'wagmi'

import { useSolanaHolders } from '@/app/[locale]/(commonLayout)/pump404/[chainId]/[address]/hooks/useSolanaHolders'
// import collectionStore from '@/store/collection-store'
// import useFusionAddress from '../address/useFusionAddress'
// import supportCollections from '@/constant/collection'
// import useGetOpenSeaNft from '@/hooks/query/nft/useGetOpenSeaNft'
// import useAlgChain from '@/hooks/useAlgChain'
// import useBaseChain from '@/hooks/useBaseChain'
// import useBSCChain from '@/hooks/useBSCChain'
import { isSolanaChain } from '@/hooks/useSolanaInfo'
// import useAlgChain from '@/hooks/useAlgChain'
import fusionAbi from '@/libs/abi/fusion.json'
// import { supportChainId } from '@/libs/contract/wagmiConf'
// import { toLowerCaseLettersOnly } from '@/libs/utils/util'
import { IAddress } from '@/types/IAddress'
import { IChain } from '@/types/IChain'

// const collection = supportCollections[0].contractAddress as IAddress

// const collectionOwnerAddress = {
//   '0x9e9fbde7c7a83c43913bddc8779158f1368f0413': '0xbC17fBf63177bC1110f460c4B1386f230d0Fcef3',
//   '0xd555498a524612c67f286df0e0a9a64a73a7cdc7': '0xcaC026DA5EE667Bc2074d26D7b9AEdb3cAea2f6B',
//   '0xad34776c9feee12a87bea6491bb58d02933a872b': '0x60B2db0001C818A73EdbE29EE6fF89Fd267bd3f3',
// }

const useTokenOwner = (tokenId: string, address?: IAddress | string, collectionAddress: IAddress | string = '0x') => {
  const [isOwner, setIsOwner] = useState<boolean>(false)
  const params = useParams()
  // const algChain = useAlgChain()
  // const baseChain = useBaseChain()
  // const bscChain = useBSCChain()
  const chainId = params?.chainId as unknown as IChain
  const issolanaChain = isSolanaChain(chainId)
  const {
    data: ownerAddress,
    isLoading,
    isSuccess,
    isError,
    error,
    refetch,
  } = useReadContract({
    address: collectionAddress as IAddress,
    abi: fusionAbi as any,
    functionName: 'ownerOf',
    chainId: Number(chainId),
    args: [tokenId],
    query: {
      enabled: (tokenId && chainId && !issolanaChain) as boolean,
      staleTime: 10000,
    },
  })
  const {
    holders,
    isSuccess: isSolSuccess,
    isLoading: isSolLoading,
    refetch: refetchSol,
  } = useSolanaHolders({
    mintAddress: tokenId || '',
    enabled: issolanaChain && !!tokenId,
  })
  // const { data } = useGetOpenSeaNft(collectionAddress, tokenId, chainId as IChain)
  const solanaOwner = useMemo(() => {
    if (!isSolSuccess || !holders || holders.length === 0) return ''
    return holders[0].ownerId
  }, [holders, isSolSuccess])
  useEffect(() => {
    if ((!issolanaChain && ownerAddress && ownerAddress === address) || (issolanaChain && solanaOwner === address)) {
      setIsOwner(true)
    } else {
      setIsOwner(false)
    }
  }, [address, ownerAddress, issolanaChain, solanaOwner])

  const errorMessage = useMemo(() => {
    return error?.shortMessage
  }, [error])

  // const owner = useMemo(() => {
  //   if (Number(chainId) != Number(algChain) && Number(chainId) != Number(baseChain) && data) {
  //     return data?.nft?.owners?.[0]?.address
  //   }
  //   return ownerAddress
  // }, [chainId, algChain, baseChain, data, ownerAddress])
  // const creator = useMemo(() => {
  //   if (Number(chainId) != Number(algChain) && Number(chainId) != Number(baseChain) && data) {
  //     return data?.nft?.creator
  //   }
  //   return ownerAddress
  // }, [chainId, algChain, baseChain, data, ownerAddress])
  return {
    ownerAddress: issolanaChain ? solanaOwner : ownerAddress,
    // creatorAddress: creator,
    isOwner,
    isLoading: issolanaChain ? isSolLoading : isLoading,
    isSuccess: issolanaChain ? isSolSuccess : isSuccess,
    isError,
    errorMessage,
    refetch: issolanaChain ? refetchSol : refetch,
  }
}

export default useTokenOwner
