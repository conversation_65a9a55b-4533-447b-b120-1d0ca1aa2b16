'use client'

import { useCallback, useState } from 'react'

import { BN } from '@coral-xyz/anchor'
import { ASSOCIATED_TOKEN_PROGRAM_ID, TOKEN_PROGRAM_ID } from '@solana/spl-token'
import { Connection, PublicKey, SystemProgram, SYSVAR_RENT_PUBKEY } from '@solana/web3.js'

import {
  getAssociatedTokenAccount,
  getFusionProgram,
  useSolanaWallet,
} from '@/app/[locale]/(commonLayout)/pump404/components/create-token/hooks/useProvider'
import { isSolanaChain } from '@/hooks/useSolanaInfo'
import { solanaRpc } from '@/libs/contract/wagmiConf'

interface ITransferTokenParams {
  fungibleMint: string
  recipient: string
  amount: string | number
  chainId: string | number
}

interface ITransferTokenResult {
  success: boolean
  signature?: string
  error?: string
}

const useSolanaTransferToken = (chainId: string | number) => {
  const connection = new Connection(solanaRpc, 'confirmed')
  const { wallet } = useSolanaWallet(connection)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')

  const transferToken = useCallback(
    async ({ fungibleMint, recipient, amount }: ITransferTokenParams): Promise<ITransferTokenResult> => {
      if (!isSolanaChain(chainId)) {
        const errorMsg = 'Invalid chain ID for Solana transfer'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      if (!connection || !wallet) {
        const errorMsg = 'Wallet not connected'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      if (!fungibleMint || !recipient || !amount) {
        const errorMsg = 'Missing required parameters'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      try {
        setIsLoading(true)
        setError('')

        let fungibleMintPubkey: PublicKey
        let recipientPubkey: PublicKey

        try {
          fungibleMintPubkey = new PublicKey(fungibleMint)
          recipientPubkey = new PublicKey(recipient)
        } catch (err) {
          throw new Error('Invalid address format')
        }

        const fusionProgram = getFusionProgram(connection, wallet)

        const [collectionPda] = PublicKey.findProgramAddressSync(
          [Buffer.from('fusion-collection'), fungibleMintPubkey.toBuffer()],
          fusionProgram.programId
        )

        const senderTokenAccount = getAssociatedTokenAccount(wallet.publicKey, fungibleMintPubkey)
        const recipientTokenAccount = getAssociatedTokenAccount(recipientPubkey, fungibleMintPubkey)

        const transferAmount = new BN(BigInt(Math.floor(Number(amount) * 1e9)).toString())

        if (transferAmount.lte(new BN(0))) {
          throw new Error('Transfer amount must be greater than 0')
        }

        const TOKEN_METADATA_PROGRAM_ID = new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s')

        console.log('Transfer parameters:', {
          fungibleMint: fungibleMintPubkey.toString(),
          collection: collectionPda.toString(),
          senderTokenAccount: senderTokenAccount.toString(),
          recipientTokenAccount: recipientTokenAccount.toString(),
          sender: wallet.publicKey.toString(),
          recipient: recipientPubkey.toString(),
          amount: transferAmount.toString(),
        })

        const tx = await fusionProgram.methods
          .transferToken(transferAmount)
          .accounts({
            fungibleMint: fungibleMintPubkey,
            collection: collectionPda,
            senderTokenAccount: senderTokenAccount,
            recipientTokenAccount: recipientTokenAccount,
            sender: wallet.publicKey,
            payer: wallet.publicKey,
            recipient: recipientPubkey,
            tokenProgram: TOKEN_PROGRAM_ID,
            associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
            tokenMetadataProgram: TOKEN_METADATA_PROGRAM_ID,
            systemProgram: SystemProgram.programId,
            rent: SYSVAR_RENT_PUBKEY,
          })
          .transaction()

        const { blockhash } = await connection.getLatestBlockhash()
        tx.recentBlockhash = blockhash
        tx.feePayer = wallet.publicKey

        const signedTx = await wallet.signTransaction(tx)
        const signature = await connection.sendRawTransaction(signedTx.serialize(), {
          skipPreflight: true,
          preflightCommitment: 'confirmed',
        })

        await connection.confirmTransaction({
          signature,
          blockhash,
          lastValidBlockHeight: (await connection.getLatestBlockhash()).lastValidBlockHeight,
        })

        console.log('Transfer successful, signature:', signature)
        return { success: true, signature }
      } catch (err: any) {
        const errorMessage = err?.message || 'Transfer failed'
        console.error('Transfer error:', err)
        setError(errorMessage)
        return { success: false, error: errorMessage }
      } finally {
        setIsLoading(false)
      }
    },
    [chainId, connection, wallet]
  )

  return {
    transferToken,
    isLoading,
    error,
  }
}

export default useSolanaTransferToken
