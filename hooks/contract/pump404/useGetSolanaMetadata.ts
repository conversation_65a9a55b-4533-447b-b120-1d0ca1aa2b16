import { useCallback, useRef, useState } from 'react'

import { fetchDigitalAsset } from '@metaplex-foundation/mpl-token-metadata'
import { mplTokenMetadata } from '@metaplex-foundation/mpl-token-metadata'
import { publicKey } from '@metaplex-foundation/umi'
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults'
import { Connection, PublicKey } from '@solana/web3.js'

import { IBondingCurvePdaAccount } from '@/app/[locale]/(commonLayout)/pump404/[chainId]/[address]/hooks/useGetBondingCurvePda'
import useGetSolanaUserNft from '@/app/[locale]/(commonLayout)/pump404/[chainId]/[address]/hooks/useGetSolanaUserNft'
import { fetchAllHoldersCount } from '@/app/[locale]/(commonLayout)/pump404/[chainId]/[address]/hooks/useSolanaHolders'
import {
  getFusionProgram,
  getPumpProgram,
  useSolanaWallet,
} from '@/app/[locale]/(commonLayout)/pump404/components/create-token/hooks/useProvider'
import { isSolanaChain } from '@/hooks/useSolanaInfo'
import { formatBn } from '@/libs/common/format'
import { solanaRpc } from '@/libs/contract/wagmiConf'
import { getBondingCurvePda, getCollectionPda, getConfigPda } from '@/libs/utils/pda'
import { ICollectionPdaAccount } from '@/types/solana/IPda'

import { IPumpInfo, ISolanaNftInfo } from './useGetBaseInfo'

const useGetSolanaMetadata = (chainId: string | number) => {
  const connection = new Connection(solanaRpc, 'confirmed')
  const { wallet } = useSolanaWallet(connection)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [metadata, setMetadata] = useState<IPumpInfo | null>(null)
  const { getSolanaUserNftInfo } = useGetSolanaUserNft(chainId)

  const lastMintAddress = useRef<string | null>(null)
  const lastUserAddress = useRef<string | null>(null)

  const getSolanaCreator = useCallback(
    async (mintAddress: string): Promise<string | null> => {
      if (!isSolanaChain(chainId)) return null
      if (!connection || !mintAddress) {
        setError('invalid connection or mintAddress')
        return null
      }
      try {
        setIsLoading(true)
        setError(null)

        const umi = createUmi(solanaRpc).use(mplTokenMetadata())

        let mintPublicKey: PublicKey
        try {
          mintPublicKey = new PublicKey(mintAddress)
        } catch (e) {
          console.error('invalid mintAddress:', mintAddress, e)
          throw new Error(`invalid mintAddress: ${mintAddress}`)
        }

        const asset = await fetchDigitalAsset(umi, publicKey(mintPublicKey.toBase58()))
        const createrAddr =
          asset.metadata.creators?.__option === 'Some' ? asset.metadata.creators.value[0]?.address ?? '' : ''
        return createrAddr
      } catch (err: any) {
        const errorMessage = err.message || 'get solana metadata error'
        console.error('Solana metadata error:', errorMessage)
        setError(errorMessage)
        return null
      } finally {
        setIsLoading(false)
      }
    },
    [connection, chainId]
  )

  const getSolanaMetadata = useCallback(
    async (mintAddress: string): Promise<IPumpInfo | null> => {
      if (!isSolanaChain(chainId)) return null
      if (!connection || !mintAddress) {
        setError('invalid connection or mintAddress')
        return null
      }
      lastMintAddress.current = mintAddress

      try {
        setIsLoading(true)
        setError(null)

        const umi = createUmi(solanaRpc).use(mplTokenMetadata())

        let mintPublicKey: PublicKey
        try {
          mintPublicKey = new PublicKey(mintAddress)
        } catch (e) {
          console.error('invalid mintAddress:', mintAddress, e)
          throw new Error(`invalid mintAddress: ${mintAddress}`)
        }

        const asset = await fetchDigitalAsset(umi, publicKey(mintPublicKey.toBase58()))
        const program = getPumpProgram(connection, wallet)
        const fusionProgram = getFusionProgram(connection, wallet)

        const configPda = getConfigPda()
        // @ts-ignore
        const configAccount = await program.account.config.fetch(configPda)

        const bondingCurvePda = getBondingCurvePda(mintPublicKey)
        const createrAddr =
          asset.metadata.creators?.__option === 'Some' ? asset.metadata.creators.value[0]?.address ?? '' : ''
        // @ts-ignore
        const bondingCurveAccount = (await program.account.bondingCurve.fetch(
          bondingCurvePda
        )) as IBondingCurvePdaAccount
        console.log('bondingCurvePda:', bondingCurveAccount)

        const collectionPda = getCollectionPda(mintPublicKey)
        // @ts-ignore
        const collectionAccount = (await fusionProgram.account.collection.fetch(collectionPda)) as ICollectionPdaAccount
        console.log('collectionAccount:', collectionAccount)

        const { nftMinted } = collectionAccount
        const { tokenTotalSupply } = bondingCurveAccount
        const holders = await fetchAllHoldersCount(mintAddress, [bondingCurvePda.toString()])

        const baseMetadata: IPumpInfo = {
          name: asset.metadata.name,
          symbol: asset.metadata.symbol,
          decimals: 9,
          creator: createrAddr,
          nftImage: '',
          logoImage: '',
          description: '',
          ipfsHash: asset.metadata.uri,
          ratio: Number(configAccount?.levelCoefficient),
          single: false,
          royalty: 0,
          royaltyReceiver: '',
          totalSupply: formatBn(tokenTotalSupply),
          lockPercent: 0,
          platform: 'FUSION',
          // nftSupply: Number(configAccount?.nftSupply),
          minted: Number(nftMinted),
          holder: holders,
        }

        //get ipfs metadata
        if (asset.metadata.uri) {
          try {
            const tokenResponse = await fetch(`${asset.metadata.uri}/0.json`)
            const nftResponse = await fetch(`${asset.metadata.uri}/1.json`)
            const tokenData = await tokenResponse.json()
            const nftData = await nftResponse.json()
            const fullMetadata: IPumpInfo = {
              ...baseMetadata,
              description: tokenData.description,
              nftImage: nftData.image,
              logoImage: tokenData.image,
            }

            setMetadata(fullMetadata)
            return fullMetadata
          } catch (uriError) {
            console.error('get ipfs metadata error:', uriError)
            setMetadata(baseMetadata)
            return baseMetadata
          }
        }

        setMetadata(baseMetadata)
        return baseMetadata
      } catch (err: any) {
        const errorMessage = err.message || 'get solana metadata error'
        console.error('Solana metadata error:', errorMessage)
        setError(errorMessage)
        return null
      } finally {
        setIsLoading(false)
      }
    },
    [connection, chainId, wallet]
  )

  const getSolanaNftdata = useCallback(
    async (mintAddress: string, userAddress: string): Promise<IPumpInfo | null> => {
      if (!isSolanaChain(chainId)) return null
      if (!connection || !mintAddress) {
        setError('invalid connection or mintAddress')
        return null
      }
      lastMintAddress.current = mintAddress
      lastUserAddress.current = userAddress

      try {
        setIsLoading(true)
        setError(null)

        const umi = createUmi(solanaRpc).use(mplTokenMetadata())

        let mintPublicKey: PublicKey
        let userPublicKey: PublicKey
        try {
          mintPublicKey = new PublicKey(mintAddress)
          userPublicKey = new PublicKey(userAddress)
        } catch (e) {
          console.error('invalid address:', mintAddress, userAddress, e)
          throw new Error(`invalid address: ${mintAddress} ${userAddress}`)
        }

        const asset = await fetchDigitalAsset(umi, publicKey(mintPublicKey.toBase58()))
        const program = getPumpProgram(connection, wallet)
        const fusionProgram = getFusionProgram(connection, wallet)

        const configPda = getConfigPda()
        // @ts-ignore
        const configAccount = await program.account.config.fetch(configPda)

        const bondingCurvePda = getBondingCurvePda(mintPublicKey)
        const createrAddr =
          asset.metadata.creators?.__option === 'Some' ? asset.metadata.creators.value[0]?.address ?? '' : ''
        // @ts-ignore
        const bondingCurveAccount = (await program.account.bondingCurve.fetch(
          bondingCurvePda
        )) as IBondingCurvePdaAccount

        const collectionPda = getCollectionPda(mintPublicKey)
        // @ts-ignore
        const collectionAccount = (await fusionProgram.account.collection.fetch(collectionPda)) as ICollectionPdaAccount
        const userNftInfo = await getSolanaUserNftInfo(mintAddress, userAddress, wallet, connection)
        const { nftMinted } = collectionAccount

        const { tokenTotalSupply } = bondingCurveAccount
        const holders = await fetchAllHoldersCount(mintAddress, [bondingCurvePda.toString()])
        const baseMetadata: IPumpInfo = {
          name: asset.metadata.name,
          symbol: asset.metadata.symbol,
          decimals: 9,
          creator: createrAddr,
          nftImage: '',
          logoImage: '',
          description: '',
          ipfsHash: asset.metadata.uri,
          ratio: Number(configAccount?.levelCoefficient),
          single: false,
          royalty: 0,
          royaltyReceiver: '',
          totalSupply: formatBn(tokenTotalSupply),
          lockPercent: 0,
          platform: 'FUSION',
          // nftSupply: Number(configAccount?.nftSupply),
          minted: formatBn(nftMinted),
          holder: holders,
        }

        //get ipfs metadata
        if (asset.metadata.uri) {
          try {
            const tokenResponse = await fetch(`${asset.metadata.uri}/0.json`)
            let solanaNftInfo: ISolanaNftInfo[] = []
            let nftImage = ''
            await Promise.all(
              userNftInfo.map(async (item) => {
                const nftResponse = await fetch(`${asset.metadata.uri}/${item.level}.json`)
                const nftData = await nftResponse.json()
                nftImage = nftData.image
                solanaNftInfo.push({
                  name: nftData.name,
                  symbol: nftData.symbol,
                  description: nftData.description,
                  twitter: nftData.twitter,
                  telegram: nftData.telegram,
                  discord: nftData.discord,
                  website: nftData.website,
                  image: nftData.image,
                  level: Number(item.level),
                  attributes: nftData.attributes,
                })
              })
            )
            const tokenData = await tokenResponse.json()
            const fullMetadata: IPumpInfo | null =
              userNftInfo.length > 0
                ? {
                    ...baseMetadata,
                    description: tokenData.description,
                    nftImage: nftImage,
                    logoImage: tokenData.image,
                    solanaNftInfo: solanaNftInfo,
                  }
                : null
            console.log('fullMetadata:', fullMetadata)

            setMetadata(fullMetadata)
            return fullMetadata
          } catch (uriError) {
            console.error('get ipfs metadata error:', uriError)
            setMetadata(baseMetadata)
            return baseMetadata
          }
        }

        setMetadata(baseMetadata)
        return baseMetadata
      } catch (err: any) {
        const errorMessage = err.message || 'get solana metadata error'
        console.error('Solana metadata error:', errorMessage)
        setError(errorMessage)
        return null
      } finally {
        setIsLoading(false)
      }
    },
    [connection, chainId, wallet]
  )

  const refetchMetadata = useCallback(async () => {
    if (lastMintAddress.current) {
      return getSolanaMetadata(lastMintAddress.current)
    }
    return null
  }, [getSolanaMetadata])

  const refetchNftdata = useCallback(() => {
    if (lastMintAddress.current && lastUserAddress.current) {
      return getSolanaNftdata(lastMintAddress.current, lastUserAddress.current)
    }
    return null
  }, [getSolanaNftdata])

  return {
    getSolanaMetadata,
    getSolanaNftdata,
    getSolanaCreator,
    refetchMetadata,
    refetchNftdata,
    metadata,
    isLoading,
    error,
  }
}

export default useGetSolanaMetadata
