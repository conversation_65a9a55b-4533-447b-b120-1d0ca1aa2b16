'use client'

import { useCallback } from 'react'

import { IHomeCollectionData } from '@/types/ITable'

interface ICollectionPersistenceData {
  collection: IHomeCollectionData | null
  attrs: Array<{ k: string; v: string[] }>
}

interface ICollectionPersistenceReturn {
  saveCollectionToStorage: (collection: IHomeCollectionData | null, attrs?: Array<{ k: string; v: string[] }>) => void
  loadCollectionFromStorage: (address?: string, chainId?: string) => ICollectionPersistenceData | null
  clearCollectionStorage: () => void
}

const COLLECTION_STORAGE_PREFIX = 'nft_collection_'

export function useCollectionPersistence(): ICollectionPersistenceReturn {
  const saveCollectionToStorage = useCallback(
    (collection: IHomeCollectionData | null, attrs: Array<{ k: string; v: string[] }> = []) => {
      if (collection) {
        const storageKey = `${COLLECTION_STORAGE_PREFIX}${collection.chainId}_${collection.address.toLowerCase()}`
        const data: ICollectionPersistenceData = {
          collection,
          attrs,
        }
        localStorage.setItem(storageKey, JSON.stringify(data))
      }
    },
    []
  )

  const loadCollectionFromStorage = useCallback(
    (address?: string, chainId?: string): ICollectionPersistenceData | null => {
      if (!address || !chainId) return null

      try {
        const storageKey = `${COLLECTION_STORAGE_PREFIX}${chainId}_${address.toLowerCase()}`
        const stored = localStorage.getItem(storageKey)
        if (stored) {
          return JSON.parse(stored) as ICollectionPersistenceData
        }
      } catch (error) {
        console.error('Failed to load collection from storage:', error)
      }
      return null
    },
    []
  )

  const clearCollectionStorage = useCallback(() => {
    // Clear all collection-related storage
    Object.keys(localStorage).forEach((key) => {
      if (key.startsWith(COLLECTION_STORAGE_PREFIX)) {
        localStorage.removeItem(key)
      }
    })
  }, [])

  return {
    saveCollectionToStorage,
    loadCollectionFromStorage,
    clearCollectionStorage,
  }
}

// Helper functions for URL parameter serialization
export function serializeAttrs(attrs: Array<{ k: string; v: string[] }>): string {
  if (!attrs || attrs.length === 0) return ''
  try {
    return encodeURIComponent(JSON.stringify(attrs))
  } catch {
    return ''
  }
}

export function deserializeAttrs(attrsString: string): Array<{ k: string; v: string[] }> {
  if (!attrsString) return []
  try {
    return JSON.parse(decodeURIComponent(attrsString)) as Array<{ k: string; v: string[] }>
  } catch {
    return []
  }
}
