'use client'

import { useRouter, useSearchParams } from 'next/navigation'

import { HomeTabKey } from '@/app/[locale]/(commonLayout)/(home)/compoents/PumpList'
import { ITabs } from '@/app/[locale]/(commonLayout)/(user)/user/[address]/page'
import { ITabKey } from '@/app/[locale]/(commonLayout)/explore/page'
import { SearchNftQueryQueryVariables } from '@/graphql/generated'
import { IGetPumpTokensReq, TSortTypeKey } from '@/types/IPump'
import { DisplayMode } from '@/types/nft'

interface ISearchParams {
  displayMode?: DisplayMode
  isSmall?: boolean
  // isFilter?: boolean
  attrs?: string
  type?: ITabKey | HomeTabKey | ITabs
  collectionAddress?: string
  collectionChainId?: string
}

export function useUpdateSearchParams(tabPrefix?: string) {
  const router = useRouter()

  const updateSearchParams = (updates: IGetPumpTokensReq & ISearchParams) => {
    const params = new URLSearchParams()

    Object.entries(updates).forEach(([key, value]) => {
      const paramKey = tabPrefix && isFilterParam(key) ? `${tabPrefix}_${key}` : key
      if (value) {
        params.set(paramKey, String(value))
      }
    })

    router.replace(`?${params.toString()}`, { scroll: false })
  }

  return updateSearchParams
}

function isFilterParam(key: string): boolean {
  const filterParams = [
    'search',
    'filterType',
    'timeFrame',
    'sortType',
    'chainId',
    'onlyFavorite',
    'userAddress',
    'selectedDropdown',
    'displayMode',
  ]
  return filterParams.includes(key)
}

export function useReadSearchParams(tabPrefix?: string) {
  const searchParams = useSearchParams()

  const readSearchParams = (): Partial<IGetPumpTokensReq> & { selectedDropdown?: TSortTypeKey } & ISearchParams => {
    const params: Partial<IGetPumpTokensReq> & { selectedDropdown?: TSortTypeKey } & ISearchParams = {}

    const getParam = (key: string) => {
      if (tabPrefix && isFilterParam(key)) {
        return searchParams.get(`${tabPrefix}_${key}`)
      }
      return searchParams.get(key)
    }

    const search = getParam('search')
    if (search) params.search = search

    const filterType = getParam('filterType')
    if (filterType) params.filterType = filterType as 'ALL' | 'FINALIZED'

    const timeFrame = getParam('timeFrame')
    if (timeFrame) params.timeFrame = timeFrame as '5MIN' | '1H' | '6H' | '1D'

    const sortType = getParam('sortType')
    if (sortType) params.sortType = sortType as 'NEWEST'

    const chainId = getParam('chainId')
    if (chainId && chainId !== 'all') {
      const numChainId = Number(chainId)
      params.chainId = isNaN(numChainId) ? chainId : numChainId
    }

    const onlyFavorite = getParam('onlyFavorite')
    if (onlyFavorite === 'true') params.onlyFavorite = true

    const userAddress = getParam('userAddress')
    if (userAddress) params.userAddress = userAddress as any

    if (params.sortType === 'NEWEST') {
      params.selectedDropdown = 'newest'
    } else if (params.timeFrame) {
      const timeFrameToDropdownMap: Record<string, TSortTypeKey> = {
        '5MIN': 'fiveminutes',
        '1H': 'onehour',
        '6H': 'sixhours',
        '1D': 'oneday',
      }
      params.selectedDropdown = timeFrameToDropdownMap[params.timeFrame]
    }

    const displayMode = getParam('displayMode')
    if (displayMode) params.displayMode = displayMode as DisplayMode

    return params
  }

  return readSearchParams
}

export function useUpdateNftSearchParams(tabPrefix?: string) {
  const router = useRouter()

  const updateSearchParams = (updates: Partial<SearchNftQueryQueryVariables> & ISearchParams) => {
    const params = new URLSearchParams()

    Object.entries(updates).forEach(([key, value]) => {
      const paramKey = tabPrefix && isNftFilterParam(key) ? `${tabPrefix}_${key}` : key
      if (value !== undefined && value !== null) {
        params.set(paramKey, String(value))
      }
    })

    router.replace(`?${params.toString()}`)
  }

  return updateSearchParams
}

function isNftFilterParam(key: string): boolean {
  const nftFilterParams = [
    'tokenId_contains',
    'contractAddress',
    'chainId',
    'price_lte',
    'price_gte',
    'priceEndTime_gt',
    'priceEndTime_lt',
    'price_not',
    'orderBy',
    'orderDirection',
    'selectedDropdown',
    'cardSize',
    // 'isFilter',
    'collectionAddress',
    'collectionChainId',
    'attrs',
  ]
  return nftFilterParams.includes(key)
}

export function useReadNftSearchParams(tabPrefix?: string) {
  const searchParams = useSearchParams()

  const readSearchParams = (): Partial<SearchNftQueryQueryVariables> &
    ISearchParams & {
      selectedDropdown?: string
      collectionAddress?: string
      collectionChainId?: string
      attrs?: string
    } => {
    const params: Partial<SearchNftQueryQueryVariables> &
      ISearchParams & {
        selectedDropdown?: string
        collectionAddress?: string
        collectionChainId?: string
        attrs?: string
      } = {}

    const getParam = (key: string) => {
      if (tabPrefix && isNftFilterParam(key)) {
        return searchParams.get(`${tabPrefix}_${key}`)
      }
      return searchParams.get(key)
    }

    const tokenId_contains = getParam('tokenId_contains')
    if (tokenId_contains) params.tokenId_contains = tokenId_contains

    const contractAddress = getParam('contractAddress')
    if (contractAddress) params.contractAddress = contractAddress

    const chainId = getParam('chainId')
    if (chainId && chainId !== 'all') {
      const numChainId = Number(chainId)
      params.chainId = isNaN(numChainId) ? undefined : numChainId
    }

    const price_lte = getParam('price_lte')
    if (price_lte) params.price_lte = price_lte

    const price_gte = getParam('price_gte')
    if (price_gte) params.price_gte = price_gte

    const priceEndTime_gt = getParam('priceEndTime_gt')
    if (priceEndTime_gt) params.priceEndTime_gt = priceEndTime_gt

    const priceEndTime_lt = getParam('priceEndTime_lt')
    if (priceEndTime_lt) params.priceEndTime_lt = priceEndTime_lt

    const price_not = getParam('price_not')
    if (price_not) params.price_not = price_not

    const orderBy = getParam('orderBy')
    if (orderBy) params.orderBy = orderBy

    const orderDirection = getParam('orderDirection')
    if (orderDirection) params.orderDirection = orderDirection
    params.selectedDropdown = 'trending'
    if (orderBy && orderDirection) {
      if (orderBy == 'level' && orderDirection == 'desc') params.selectedDropdown = 'trending'
      if (orderBy == 'priceStartTime' && orderDirection == 'desc') params.selectedDropdown = 'recently'
      if (orderBy == 'price' && orderDirection == 'asc') params.selectedDropdown = 'low'
      if (orderBy == 'price' && orderDirection == 'desc') params.selectedDropdown = 'high'
    }

    const isSmall = getParam('isSmall')
    if (isSmall) params.isSmall = isSmall === 'true'

    // const isFilter = getParam('isFilter')
    // if (isFilter) params.isFilter = isFilter === 'true'

    // Collection related parameters
    const collectionAddress = getParam('collectionAddress')
    if (collectionAddress) params.collectionAddress = collectionAddress

    const collectionChainId = getParam('collectionChainId')
    if (collectionChainId) params.collectionChainId = collectionChainId

    const attrs = getParam('attrs')
    if (attrs) params.attrs = attrs

    return params
  }

  return readSearchParams
}
