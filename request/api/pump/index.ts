import request from '@/request/request'
import { IAddress } from '@/types/IAddress'
import { <PERSON><PERSON><PERSON><PERSON> } from '@/types/IChain'
import {
  IGetPumpTokensReqWithPage,
  IMarqueeItem,
  IPumpCreateparams,
  IPumpToken,
  IRankingList,
  pumpTokensRes,
} from '@/types/IPump'

// import { IAddress } from '@/types/IAddress'

const apiUrl = process.env.NEXT_PUBLIC_MEATADATA_API
const apiUrl1 = process.env.NEXT_PUBLIC_REQUEST_API
const baseScanKey = process.env.NEXT_PUBLIC_BASESACN_API
const bscScanKey = process.env.NEXT_PUBLIC_BSCSCAN_API_KEY
const birdeyeApiKey = process.env.NEXT_PUBLIC_BIRDEYE_API_KEY
const indexUrl = process.env.NEXT_PUBLIC_GRAPH_URL

function objectToQueryString(obj: any) {
  return Object.keys(obj)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
    .join('&')
}

const getCoinRates = () => {
  return request.get(`${apiUrl1}/token/coin/rates`)
}

export const getMarquee = () => {
  return request.get<IMarqueeItem[]>(`${apiUrl}api/pump404/marquee`)
}

export const getPumpTokens = (params: IGetPumpTokensReqWithPage) => {
  const queryStr = objectToQueryString(params)
  return request.get<pumpTokensRes>(`${apiUrl1}/collection/search?${queryStr}`)
}

export const getPumpKing = () => {
  return request.get<IPumpToken>(`${apiUrl1}/collection/king`)
}
// create
export const collectionCreate = (params: IPumpCreateparams) => {
  return request.post('/collection/create', params)
}

// searchSaved
export const collectionSaved = () => {
  return request.get('/collection/saved')
}

// collection find
export const collectionFind = (chainId: IChain, contractAddress: IAddress | string) => {
  return request.get(`/collection/find?chainId=${chainId}&&contractAddress=${contractAddress}`)
}

// check in fusion
export const checkInFusion = (contractAddresses: string[]) => {
  return request.post(`/collection/checkInFusion`, { contractAddresses })
}

export const getRankingList = () => {
  return request.get<IRankingList>('/collection/rank?size=10')
}
export async function getEthPrice() {
  // 先尝试从自己的接口获取汇率
  try {
    const ratesResponse = await getCoinRates()
    console.log('Internal rates response:', ratesResponse)

    // 尝试多种可能的数据结构
    const ethPrice =
      (ratesResponse?.data as any)?.ETH ||
      (ratesResponse?.data as any)?.eth ||
      (ratesResponse as any)?.ETH ||
      (ratesResponse as any)?.eth

    if (ethPrice && Number(ethPrice) > 0) {
      console.log('ETH Price from internal API:', ethPrice)
      return Number(ethPrice)
    }
  } catch (error) {
    console.log('Internal API failed, falling back to external API:', error)
  }

  // 如果内部接口失败，使用外部 API
  const url = `https://api.basescan.org/api?module=stats&action=ethprice&apikey=${baseScanKey}`

  try {
    const response = await fetch(url)
    const data = await response.json()

    if (data.status === '1') {
      const ethPrice = data.result.ethusd
      console.log('ETH Price from external API:', ethPrice)
      return ethPrice
    } else {
      console.error('Failed to fetch ETH price:', data.message)
      return null
    }
  } catch (error) {
    console.error('Error fetching ETH price:', error)
    return null
  }
}

export async function getBnbPrice() {
  // 先尝试从自己的接口获取汇率
  try {
    const ratesResponse = await getCoinRates()
    console.log('Internal rates response:', ratesResponse)

    // 尝试多种可能的数据结构
    const bnbPrice =
      (ratesResponse?.data as any)?.BNB ||
      (ratesResponse?.data as any)?.bnb ||
      (ratesResponse as any)?.BNB ||
      (ratesResponse as any)?.bnb

    if (bnbPrice && Number(bnbPrice) > 0) {
      console.log('BNB Price from internal API:', bnbPrice)
      return Number(bnbPrice)
    }
  } catch (error) {
    console.log('Internal API failed, falling back to external API:', error)
  }

  // 如果内部接口失败，使用外部 API
  const url = `https://api.bscscan.com/api?module=stats&action=bnbprice&apikey=${bscScanKey}`

  try {
    const response = await fetch(url)
    const data = await response.json()

    if (data.status === '1') {
      const bnbPrice = data.result.ethusd
      console.log('BNB Price from external API:', bnbPrice)
      return bnbPrice
    } else {
      console.error('Failed to fetch BNB price:', data.message)
      return null
    }
  } catch (error) {
    console.error('Error fetching BNB price:', error)
    return null
  }
}

export async function getSolanaUsdtPrice() {
  // 先尝试从自己的接口获取汇率
  try {
    const ratesResponse = await getCoinRates()
    if (ratesResponse?.data?.SOL) {
      console.log('SOL Price from internal API:', ratesResponse.data.SOL)
      return ratesResponse.data.SOL
    }
  } catch (error) {
    console.log('Internal API failed, falling back to external API:', error)
  }

  // 如果内部接口失败，使用外部 API
  const solanaUsdtAddress = 'So11111111111111111111111111111111111111112'
  const url = `https://public-api.birdeye.so/defi/price?address=${solanaUsdtAddress}`

  try {
    const response = await fetch(url, {
      headers: {
        'X-API-KEY': birdeyeApiKey || '',
      },
    })
    const data = await response.json()

    if (data.success) {
      const usdtPrice = data.data.value
      console.log('Solana USDT Price from external API:', usdtPrice)
      return usdtPrice
    } else {
      console.error('Failed to fetch Solana USDT price:', data.message)
      return null
    }
  } catch (error) {
    console.error('Error fetching Solana USDT price:', error)
    return null
  }
}

export const getPumpStats = (tokenAddress: IAddress, createTime: string) => {
  return request.get(`${indexUrl}/api/pump/stat?token_address=${tokenAddress}&create_time=${createTime}`)
}
