'use client'

import React, { memo, useEffect, useRef } from 'react'

import { useTranslate } from '@tolgee/react'
import Link from 'next/link'
import { useDebounceCallback } from 'usehooks-ts'
import { formatEther, formatUnits } from 'viem'

import NftCover from '@/components/NftCover/index-pump'
import Marquee from '@/components/ui/marquee'

import useGetMarquee from '../../hooks/useGetMarquee'
import useWebSocket from '../../hooks/useWebSocket'
import useWssAddress from '@/hooks/contract/address/useWssAddress'
import useGetUserInfo from '@/hooks/query/user/useGetUserInfo'
import useLanguage from '@/hooks/useLanguage'
import { isSolanaChain } from '@/hooks/useSolanaInfo'
import useTouchDevice from '@/hooks/useTouchDevice'
// import { useTranslate } from '@tolgee/react'
import { cn } from '@/lib/utils'
import { getChainInfo } from '@/libs/common/chain'
// import { cutNumFotmat } from '@/libs/common/cuTNum'
import { formatPrice, formatTimeStampToDay } from '@/libs/common/format'
import { IAddress } from '@/types/IAddress'
// import { IAddress } from '@/types/IAddress'
import { IMarqueeItem, TMarqueeEventType } from '@/types/IPump'

const textColorMap = {
  CREATE_ERC404: 'text-brand',
  SOLD_TOKEN: 'text-error',
  BOUGHT_TOKEN: 'text-success',
}

const ItemCard = memo(
  ({ eventType, fromId, cost, name, logoImage, createTime, tokenAddress, chainId }: IMarqueeItem) => {
    const { t } = useTranslate('pump')
    const { lng } = useLanguage()
    const { userInfo } = useGetUserInfo(fromId as IAddress)
    const { getChainCurrency } = getChainInfo()
    const formatId = (id: string) => {
      const start = id.slice(2, 6)
      const end = id.slice(-4)
      return `${start}...${end}`
    }
    ItemCard.displayName = 'ItemCard'
    // const openUserPage = (e:any) => {
    //    const path='https://test-app.likn.co/zh/0x4a1156fb5f62e22c847ac05e54f2cd9621d47c0b/4a11ac9e36b2715b19859610691b424d62a27c0b?back=back'
    //     e.preventDefault()
    //     e.stopPropagation()
    //     window.open(path, '_blank')
    // }

    // const openDetail = (e:any) => {
    //    const path='https://test-app.likn.co/zh/0x4a1156fb5f62e22c847ac05e54f2cd9621d47c0b/4a11ac9e36b2715b19859610691b424d62a27c0b?back=back'
    //     e.preventDefault()
    //     e.stopPropagation()
    //     window.open(path, '_blank')
    // }

    const formatType = (eventType: TMarqueeEventType) => {
      switch (eventType) {
        case 'BOUGHT_TOKEN':
          return t('bought')
        case 'SOLD_TOKEN':
          return t('sold')
        case 'CREATE_ERC404':
          return t('created')
        default:
          return ''
      }
    }

    const ItemMap = {
      CREATE_ERC404: (
        <>
          <Link
            href={`/${lng}/user/${fromId}`}
            passHref
            legacyBehavior
            className="hover:scale-95 hover:cursor-pointer active:scale-95"
          >
            <a className="flex items-center">
              <span className="inline-block max-w-[100px] truncate text-quaternary">
                {userInfo ? userInfo?.displayName || formatId(fromId) : formatId(fromId)}
              </span>{' '}
            </a>
          </Link>
          <span className={cn(textColorMap[eventType])}>{formatType(eventType)}</span>
          <div className="h-[20px] w-[20px] flex-shrink-0 flex-grow-0">
            <NftCover className="h-[20px] w-[20px]" cover={logoImage} />
          </div>
          <Link
            href={`/${lng}/pump404/${chainId}/${tokenAddress}`}
            passHref
            legacyBehavior
            className="hover:scale-95 hover:cursor-pointer active:scale-95"
          >
            <a className="flex items-center">
              <span className="inline-block max-w-[100px] truncate text-primary"> {name}</span>
            </a>
          </Link>
          <span className="text-quaternary">
            {t('on')} {formatTimeStampToDay(createTime)}
          </span>
        </>
      ),
      SOLD_TOKEN: (
        <>
          <Link
            href={`/${lng}/user/${fromId}`}
            passHref
            legacyBehavior
            className="hover:scale-95 hover:cursor-pointer active:scale-95"
          >
            <a className="flex items-center">
              <span className="inline-block max-w-[100px] truncate text-quaternary">
                {userInfo ? userInfo?.displayName || formatId(fromId) : formatId(fromId)}
              </span>{' '}
            </a>
          </Link>
          <span className={cn(textColorMap[eventType])}>{formatType(eventType)}</span>
          <span className="text-quaternary">
            {' '}
            {formatPrice(
              isSolanaChain(chainId) ? formatUnits(BigInt(cost), 9) : formatEther(BigInt(cost)),
              getChainCurrency(chainId)
            )}{' '}
            {getChainCurrency(chainId)} {t('of')}{' '}
          </span>
          <div className="h-[20px] w-[20px] flex-shrink-0 flex-grow-0">
            <NftCover className="h-[20px] w-[20px]" cover={logoImage} />
          </div>
          <Link
            href={`/${lng}/pump404/${chainId}/${tokenAddress}`}
            passHref
            legacyBehavior
            className="hover:scale-95 hover:cursor-pointer active:scale-95"
          >
            <a className="flex items-center">
              <span className="inline-block max-w-[100px] truncate text-primary"> {name}</span>
            </a>
          </Link>
          <span className="text-quaternary">
            {t('on')} {formatTimeStampToDay(createTime)}
          </span>
        </>
      ),
      BOUGHT_TOKEN: (
        <>
          <Link
            href={`/${lng}/user/${fromId}`}
            passHref
            legacyBehavior
            className="hover:scale-95 hover:cursor-pointer active:scale-95"
          >
            <a className="flex items-center">
              <span className="inline-block max-w-[100px] truncate text-quaternary">
                {userInfo ? userInfo?.displayName || formatId(fromId) : formatId(fromId)}
              </span>
            </a>
          </Link>
          <span className={cn(textColorMap[eventType])}>{formatType(eventType)}</span>
          <span className="text-quaternary">
            {' '}
            {formatPrice(
              isSolanaChain(chainId) ? formatUnits(BigInt(cost), 9) : formatEther(BigInt(cost)),
              getChainCurrency(chainId)
            )}{' '}
            {getChainCurrency(chainId)} {t('of')}{' '}
          </span>
          <div className="h-[20px] w-[20px] flex-shrink-0 flex-grow-0">
            <NftCover className="h-[20px] w-[20px]" cover={logoImage} />
          </div>
          <Link
            href={`/${lng}/pump404/${chainId}/${tokenAddress}`}
            passHref
            legacyBehavior
            className="hover:scale-95 hover:cursor-pointer active:scale-95"
          >
            <a className="flex items-center">
              <span className="inline-block max-w-[100px] truncate text-primary"> {name}</span>
            </a>
          </Link>
          <span className="text-quaternary">
            {t('on')} {formatTimeStampToDay(createTime)}
          </span>
        </>
      ),
    }
    return <div className="tracking-xs flex items-center gap-1.5 text-sm leading-sm ">{ItemMap[eventType]}</div>
  }
)

const Lamp = () => {
  const isTouchDevice = useTouchDevice()
  const wssAddress = useWssAddress()
  const { data, isSuccess } = useGetMarquee()
  const [queue, setQueue] = React.useState<IMarqueeItem[]>([])
  const timer = useRef<any>(null)

  const updateQueue = useDebounceCallback(() => {
    console.log('ws updateQueue setInterval eventArr.current.length:', eventArr.current.length)
    if (eventArr.current.length > 0) {
      const newData = [...queue, ...eventArr.current].slice(-10)
      console.log('ws:newData:', newData)
      setQueue(newData)
      eventArr.current = []
    }
  }, 500)

  useEffect(() => {
    if (timer.current) clearInterval(timer.current)
    timer.current = setInterval(() => updateQueue(), 500)
    return () => {
      if (timer.current) clearInterval(timer.current)
    }
  }, [queue])

  const handleEvent = useDebounceCallback((event: any) => {
    try {
      const activity = JSON.parse(event.data)
      eventArr.current.push(activity)
      console.log('ws 消息来了 eventArr.current.length:', eventArr.current.length)
    } catch (e) {
      console.log(e)
    }
  }, 500)
  const { connect, eventArr } = useWebSocket({ wssAddress: wssAddress, maxReconnectNum: 3, handleEvent: handleEvent })
  useEffect(() => {
    if (data.length > 0) {
      console.log('ws 连接前数据：', data)
      const newData = [...queue, ...data].slice(-10)

      setQueue(newData)
    }
  }, [data])

  useEffect(() => {
    if (!isSuccess) return
    console.log('ws 初始数据已经请求成功了，开始创建ws连接')
    connect()
    // setInterval(() => {
    //   const event = {
    //     data: JSON.stringify({
    //       eventType: 'BOUGHT_TOKEN',
    //       fromId: '0x03B168460dae80D96c3FF8D67a01C69d36Ac78bww0',
    //       tokenAddress: '0xe152a27b518968bac978e2be9c571b50w31dac81a',
    //       amount: '104068000000000000000000',
    //       cost: '900000000000000000',
    //       createTime: new Date().getTime(),
    //       name: new Date().getTime(),
    //       logoImage: 'https://images.likn.co/cover/0x4a1156fb5f62e22c847ac05e54f2cd9621d47c0b1733484193799.jpeg',
    //     }),
    //   }
    //   handleEvent(event)
    // }, 300)
  }, [isSuccess])
  return (
    <div className="relative -left-4 h-[44px] w-[calc(100%+32px)] md:-left-7 md:w-[calc(100%+56px)] xl:-left-8 xl:w-[calc(100%+64px)]">
      {queue.length < 1 ? (
        <Marquee pauseOnHover={!isTouchDevice} className="h-11 w-full gap-6 bg-buttonTertiary py-[10px]"></Marquee>
      ) : (
        <Marquee
          pauseOnHover={!isTouchDevice}
          className="h-11 w-full gap-6 bg-buttonTertiary py-[10px]"
          duration={queue.length * 5}
          key={`${queue[0].tokenAddress}${queue[0].createTime}${queue[0].fromId}`}
        >
          {queue.reduce((arr: React.ReactNode[], review, index) => {
            return [
              ...arr,
              <ItemCard {...review} key={`activity_${index}`} />,
              <div className="h-full w-[1px] bg-borderSecondary" key={`line_${index}`}></div>,
            ]
          }, [])}
        </Marquee>
      )}
    </div>
  )
}

export default memo(Lamp)
