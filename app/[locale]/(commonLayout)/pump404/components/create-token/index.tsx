import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import toast from 'react-hot-toast'
import { TbAlertTriangleFilled } from 'react-icons/tb'

import { useDisclosure } from '@nextui-org/react'
import { useTranslate } from '@tolgee/react'
import { CircleHelp } from 'lucide-react'
import { X } from 'lucide-react'
import { twMerge } from 'tailwind-merge'
import { useDebounceCallback } from 'usehooks-ts'
import { formatEther } from 'viem'

import ChainIcon from '@/components/ChainInfo/ChainIcon'
import SwapIcon from '@/components/ChainInfo/SwapIcon'
import Button from '@/components/library/Button'
import Input from '@/components/library/Input'
import NftTooltip from '@/components/library/NftTooltip'

import userState from '@/store/user-store'

import useGetBondingCurvePurchase from '../../[chainId]/[address]/hooks/useGetBondingCurvePurchase'
import BasicForm from './components/BasicForm'
import BlockChainForm from './components/BlockChainForm'
import { CreateTokenContext, IBasicInfo, IFileInfo } from './components/CreateTokenContext'
import FollowSteps, { ISteps } from './components/FollowSteps'
import Links from './components/Links'
import TitleComp, { ITitleComp } from './components/TitleComp'
import UploadForm from './components/UploadForm'
import useCreatePump from './hooks/useCreatePump'
import useGetSavedPump from './hooks/useGetSavedPump'
import useSwitchChain from './hooks/useSwitchChain'
import { chainInfo } from '@/constant/chainInfo'
import useConnect from '@/hooks/useConnect'
import useIsPhone from '@/hooks/useIsPhone'
import useRouter from '@/hooks/useRouter'
import { isSolanaChain } from '@/hooks/useSolanaInfo'
import { useToast } from '@/hooks/useToast'
import useUniversalBalance from '@/hooks/useUniversalBalance'
import { useUniversalSignMessage } from '@/hooks/useUniversalSignMessage'
import { getChainInfo } from '@/libs/common/chain'
import { decimalInfo, sanitizeForJSON } from '@/libs/common/format'
import { useAppKitAccount, useAppKitNetwork } from '@/libs/contract/wagmiConf'
import { uploadToCloudFlareForPump } from '@/libs/utils/UploadCloudFlare'
// import { replaceSpecialCharactersWithSpace } from '@/libs/common/format'
import { checkNumberAndFormatZero } from '@/libs/vaildata/number'
import { collectionCreate } from '@/request/api/pump'
import { IAddress } from '@/types/IAddress'
import { IBlockchain, IChain } from '@/types/IChain'
import { IPumpCreateparams } from '@/types/IPump'

const CreateToken = () => {
  const { t } = useTranslate('pump')
  const { t: tCommon } = useTranslate('common')
  const isPhone = useIsPhone('(max-width: 768px)')
  const { lngPush } = useRouter()
  const { address } = useAppKitAccount()
  const { withConnect } = useConnect()
  const setManualLogout = userState((state) => state.setManualLogout)
  const { chainId: currentChain } = useAppKitNetwork()
  const { switchChain } = useSwitchChain()
  const { data: saveCollection, refetch: saveRefetch } = useGetSavedPump(address as IAddress)
  const [basicInfo, setBasicInfo] = useState<IBasicInfo | null>(null)
  const [fileInfos, setFileInfos] = useState<IFileInfo[]>([{ type: 'logo' }, { type: 'nft' }])
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure()
  const [currentStep, setCurrentStep] = useState<number>(0)
  const { errorToast, successToast } = useToast()
  const [blockchain, setBlockchain] = useState<IBlockchain>('alg')
  const { getChainIdByBlockchain, getScanUrl, getChainName, getSwapName, getBlockChainName, getChainCurrency } =
    getChainInfo()
  const { signMessage } = useUniversalSignMessage()
  const { createPump, isError, errorInfo, isSuccess, erc404Token, data: transactionData } = useCreatePump(blockchain)
  const [tokenAmount, setTokenAmount] = useState<bigint>()
  const { data: balance, isLoading: isBalanceLoading } = useUniversalBalance(
    getChainIdByBlockchain(blockchain) as IChain
  )
  const { data: tokens, isSuccess: isTokenSuccess } = useGetBondingCurvePurchase(
    '0',
    basicInfo?.initBuy || '',
    getChainIdByBlockchain(blockchain) as IChain
  )
  const [deployId, setDeployId] = useState<number | null>(null)
  const [submitError, setSubmitError] = useState<boolean>(false)
  const [showLinks, setShowLinks] = useState<boolean>(false)
  const [websiteUrl, setWebsiteUrl] = useState<string>('')
  const [twitterUrl, setTwitterUrl] = useState<string>('')
  const [discordUrl, setDiscordUrl] = useState<string>('')
  const [telegramUrl, setTelegramUrl] = useState<string>('')
  const [isSave, setIsSave] = useState<boolean>(false)
  const [isModified, setIsModified] = useState<boolean>(false)
  const initialValuesRef = useRef({
    initialBasicInfo: null as IBasicInfo | null,
    initialFileInfos: [] as IFileInfo[],
    initialShowLinks: false,
    initialUrls: {
      website: '',
      twitter: '',
      discord: '',
      telegram: '',
    },
  })
  const [saveLoading, setSaveLoading] = useState<boolean>(false)
  const titleArr: ITitleComp[] = useMemo(
    () => [
      {
        icon: <ChainIcon size={20} blockchain={blockchain} className="h-5 w-5" />,
        title: getChainName(getChainIdByBlockchain(blockchain) as IChain),
        desc: t('blockchain'),
      },
      {
        icon: <SwapIcon size={20} className="h-5 w-5 rounded-full" blockchain={blockchain} />,
        title: blockchain == 'alg' ? 'Fusion' : getSwapName(getChainIdByBlockchain(blockchain) as IChain),
        desc: t('dex_swap'),
        tips: t('dex_swap_tips'),
      },
      {
        title: '1,000,000,000',
        desc: t('token_supply'),
      },
      {
        title: '10,000',
        desc: t('nft_supply'),
      },
      {
        title: '100,000 Token',
        desc: t('nft_generation_coefficient'),
        tips: t('tokens_tips'),
      },
    ],
    [blockchain, t]
  )
  const steps: ISteps[] = useMemo(
    () => [
      {
        title: t('upload'),
        desc: t('upload_modal_desc'),
      },
      {
        title: 'Pump',
        desc: t('moda_pump_desc'),
      },
      {
        title: t('done'),
      },
    ],
    [t]
  )
  const userBalance = useMemo(() => {
    return !isBalanceLoading && balance !== undefined ? balance : 0
  }, [isBalanceLoading, balance])
  useEffect(() => {
    if (isTokenSuccess) {
      setTokenAmount(tokens as bigint)
    }
  }, [isTokenSuccess, tokens])
  useEffect(() => {
    if (saveCollection?.code == 200 && saveCollection?.data) {
      const data = saveCollection?.data as IPumpCreateparams
      if (!data?.isDeployed && !isSave && !isModified) {
        setBlockchain(getBlockChainName(data?.chainId) as IBlockchain)
        setBasicInfo({
          name: data?.name || '',
          description: data?.description || '',
          symbol: data?.symbol || '',
          initBuy: data?.initialBuy || '',
        })
        fileInfos?.map((item) => {
          if (item?.type == 'logo') {
            item.uploadUrl = data?.logoImage || ''
          }
          if (item?.type == 'nft') {
            item.uploadUrl = data?.nftImage?.length > 0 ? data?.nftImage?.[0] : ''
          }
        })
        setFileInfos([...fileInfos])
        setShowLinks(data?.withExternalLink || false)
        setWebsiteUrl(data?.websiteUrl || '')
        setTwitterUrl(data?.twitterUrl || '')
        setDiscordUrl(data?.discordUrl || '')
        setTelegramUrl(data?.telegramUrl || '')
        initialValuesRef.current = {
          initialBasicInfo: {
            name: data.name || '',
            symbol: data.symbol || '',
            description: data.description || '',
            initBuy: data.initialBuy || '',
          },
          initialFileInfos: [
            { type: 'logo', uploadUrl: data.logoImage || '' },
            { type: 'nft', uploadUrl: data.nftImage?.[0] || '' },
          ],
          initialShowLinks: data.withExternalLink || false,
          initialUrls: {
            website: data.websiteUrl || '',
            twitter: data.twitterUrl || '',
            discord: data.discordUrl || '',
            telegram: data.telegramUrl || '',
          },
        }
      }
    } else if (saveCollection?.code == 401) {
      setManualLogout(false)
      signMessage('I want to login on FUSION. I accept the FUSION Terms of Service.')
    }
  }, [saveCollection])
  useEffect(() => {
    if (basicInfo) {
      const hasChanged =
        JSON.stringify(basicInfo) !== JSON.stringify(initialValuesRef.current.initialBasicInfo) ||
        JSON.stringify(fileInfos) !== JSON.stringify(initialValuesRef.current.initialFileInfos) ||
        showLinks !== initialValuesRef.current.initialShowLinks ||
        websiteUrl !== initialValuesRef.current.initialUrls.website ||
        twitterUrl !== initialValuesRef.current.initialUrls.twitter ||
        discordUrl !== initialValuesRef.current.initialUrls.discord ||
        telegramUrl !== initialValuesRef.current.initialUrls.telegram

      setIsModified(hasChanged)
    }
  }, [basicInfo, fileInfos, showLinks, websiteUrl, twitterUrl, discordUrl, telegramUrl])
  const vaildate = useCallback(
    (type: 'create' | 'draft') => {
      let flag = true
      if (!basicInfo?.name) return false
      if (!basicInfo?.symbol) return false
      if (type === 'create') {
        fileInfos?.map((item) => {
          if (!item.file && !item.uploadUrl) {
            flag = false
          }
        })
      }
      if (
        type === 'create' &&
        basicInfo?.initBuy &&
        Number(basicInfo?.initBuy) > 0 &&
        (Number(basicInfo?.initBuy) < decimalInfo[getChainCurrency(chainId) || 'ALG'] ||
          !(Number(formatEther((tokenAmount as bigint) || BigInt(0))) > 0))
      ) {
        return false
      }
      if (showLinks) {
        const urlRegex = /^(https?:\/\/)?([\w\-]+\.)+[\w\-]+(\/[\w\-]*)*\/?$/
        const twitterRegex = /^@[\w]+$/i //
        const discordRegex = /^https?:\/\/(www\.)?discord\.gg\/[\w]+$/i
        const telegramRegex = /^https?:\/\/(www\.)?t\.me\/[\w]+$/i
        if (websiteUrl && !urlRegex.test(websiteUrl)) {
          flag = false
        }
        if (twitterUrl && !twitterRegex.test(twitterUrl)) {
          flag = false
        }
        if (discordUrl && !discordRegex.test(discordUrl)) {
          flag = false
        }
        if (telegramUrl && !telegramRegex.test(telegramUrl)) {
          flag = false
        }
      }
      return flag
    },
    [
      basicInfo?.name,
      basicInfo?.symbol,
      basicInfo?.initBuy,
      tokenAmount,
      discordUrl,
      fileInfos,
      showLinks,
      telegramUrl,
      twitterUrl,
      websiteUrl,
    ]
  )
  const UploadImage = useCallback(async () => {
    try {
      const result = await Promise.all(
        fileInfos.map(async (item) => {
          if (item?.uploadUrl) {
            return { ...item }
          }
          const nfturl = await uploadToCloudFlareForPump(item.file as File)
          return { ...item, uploadUrl: nfturl }
        })
      )
      setFileInfos([...result])
      setCurrentStep(1)
      return result
    } catch (error: any) {
      onClose()
      setCurrentStep(0)
      errorToast(error?.msg || error?.message)
      return null
    }
  }, [errorToast, fileInfos])
  const deployPump = useCallback(
    withConnect(async () => {
      if (currentChain?.toString() !== chainId.toString()) {
        await switchChain(chainId as IChain)
        if (isSolanaChain(currentChain) || isSolanaChain(chainId)) {
          return
        }
      }
      setCurrentStep(0)
      if (vaildate('create')) {
        try {
          if (userBalance < Number(basicInfo?.initBuy)) {
            errorToast(t('balance_error_fee'))
            return
          }
          onOpen()
          setSubmitError(false)
          const result = await UploadImage()
          console.log('result', result)
          const params: IPumpCreateparams = {
            dexName: 'fusion',
            name: basicInfo?.name?.replaceAll('"', '\\u0022') || '',
            symbol: basicInfo?.symbol?.replaceAll('"', '\\u0022') || '',
            description: basicInfo?.description?.replaceAll('"', '\\u0022') || '',
            contractAddress: '',
            chainName: chainInfo?.[getChainIdByBlockchain(blockchain) as IChain]?.name || '',
            chainId: getChainIdByBlockchain(blockchain) as IChain,
            tokenSupply: '10000000000',
            nftSupply: 10000,
            coefficient: 100000,
            initialBuy: basicInfo?.initBuy || '',
            logoImage: result?.find((item) => item.type == 'logo')?.uploadUrl || '',
            nftImage: result?.find((item) => item.type == 'nft')?.uploadUrl
              ? [result?.find((item) => item.type == 'nft')?.uploadUrl as string]
              : [],
            isDeployed: false,
            withExternalLink: showLinks,
            websiteUrl,
            twitterUrl,
            discordUrl,
            telegramUrl,
          }
          if (saveCollection?.data?.id && !saveCollection?.data?.isDeployed) {
            params.id = saveCollection?.data?.id
          }
          const data = await collectionCreate(params)
          if (data?.code == 200 && data?.data) {
            setDeployId(data?.data?.id)
            createPump({
              name: sanitizeForJSON(basicInfo?.name?.replaceAll('"', '\\u0022') as string),
              symbol: sanitizeForJSON(basicInfo?.symbol?.replaceAll('"', '\\u0022') as string),
              nftImage: result?.find((item) => item.type == 'nft')?.uploadUrl as string,
              logoImage: result?.find((item) => item.type == 'logo')?.uploadUrl as string,
              description: sanitizeForJSON((basicInfo?.description?.replaceAll('"', '\\u0022') || '') as string),
              ipfsHash: data?.data?.ipfsData ? data?.data?.ipfsData?.cid : '',
              single: true,
              royalty: 0,
              royaltyReceiver: address as IAddress,
              bondingCurveType: 0,
              price: 0,
              ratio: 100000,
              totalSupply: 1000000000,
              lockPercent: 20,
              platform: 'fusion',
              initBuy: (basicInfo?.initBuy || '0') as string,
            })
          } else {
            onClose()
            errorToast(data?.msg)
          }
        } catch (error: any) {
          onClose()
          errorToast(error?.msg || error?.message)
        }
      } else {
        setSubmitError(true)
      }
    }),
    [
      vaildate,
      onOpen,
      UploadImage,
      basicInfo?.name,
      basicInfo?.symbol,
      basicInfo?.description,
      basicInfo?.initBuy,
      blockchain,
      showLinks,
      websiteUrl,
      twitterUrl,
      discordUrl,
      telegramUrl,
      saveCollection?.data?.id,
      saveCollection?.data?.isDeployed,
      createPump,
      address,
      onClose,
      errorToast,
      withConnect,
    ]
  )

  useEffect(() => {
    if (isSuccess) {
      setCurrentStep(2)
    }
  }, [isSuccess, t])
  const chainId = useMemo(() => getChainIdByBlockchain(blockchain) as IChain, [blockchain])
  const createCallback = useCallback(() => {
    let newTwitterUrl = twitterUrl
    if (showLinks && newTwitterUrl.startsWith('@')) {
      newTwitterUrl = `https://x.com/${newTwitterUrl?.split('@')?.[1] || newTwitterUrl}`
    }
    const params: IPumpCreateparams = {
      dexName: 'fusion',
      name: basicInfo?.name?.replaceAll('"', '\\u0022') || '',
      symbol: basicInfo?.symbol?.replaceAll('"', '\\u0022') || '',
      description: basicInfo?.description?.replaceAll('"', '\\u0022') || '',
      contractAddress: erc404Token as string,
      chainName: chainInfo?.[getChainIdByBlockchain(blockchain) as IChain]?.name || '',
      chainId: getChainIdByBlockchain(blockchain) as IChain,
      tokenSupply: '10000000000',
      nftSupply: 10000,
      coefficient: 100000,
      initialBuy: basicInfo?.initBuy || '',
      logoImage: fileInfos?.find((item) => item.type == 'logo')?.uploadUrl || '',
      nftImage: fileInfos?.find((item) => item.type == 'nft')?.uploadUrl
        ? [fileInfos?.find((item) => item.type == 'nft')?.uploadUrl as string]
        : [],
      isDeployed: true,
      withExternalLink: showLinks,
      websiteUrl,
      twitterUrl: newTwitterUrl,
      discordUrl,
      telegramUrl,
    }
    if ((saveCollection?.data?.id && !saveCollection?.data?.isDeployed) || deployId) {
      params.id = saveCollection?.data?.id || deployId
    }
    collectionCreate(params)
      .then(() => {
        successToast(t('deploy_success'))
        lngPush(`/pump404/${chainId}/${isSolanaChain(chainId) ? erc404Token : erc404Token?.toLocaleLowerCase()}`)
        onClose()
      })
      .catch((err) => {
        errorToast(err?.msg || err?.message || err)
        onClose()
      })
  }, [
    twitterUrl,
    showLinks,
    basicInfo?.name,
    basicInfo?.symbol,
    basicInfo?.description,
    basicInfo?.initBuy,
    erc404Token,
    blockchain,
    fileInfos,
    websiteUrl,
    discordUrl,
    telegramUrl,
    saveCollection?.data?.id,
    saveCollection?.data?.isDeployed,
    deployId,
    successToast,
    t,
    lngPush,
    chainId,
    onClose,
    errorToast,
  ])
  const debounceCallback = useDebounceCallback(() => {
    createCallback()
  }, 500)
  useEffect(() => {
    if (isSuccess && erc404Token) {
      debounceCallback()
    }
  }, [isSuccess, erc404Token])
  useEffect(() => {
    if (isError && errorInfo) {
      console.log('errorInfo', errorInfo)
      onClose()
      setCurrentStep(0)
      if (transactionData) {
        const exploreUrl = getScanUrl(currentChain as IChain, transactionData)
        toast.custom(
          (to) => (
            <div
              className={`${
                to.visible ? 'animate-enter' : 'animate-leave'
              } pointer-events-auto flex w-full flex-row items-start justify-between gap-3 rounded-2xl bg-bgPrompt p-4 sm:w-[350px]`}
            >
              <div className="flex flex-row items-start gap-3">
                <div className="h-6 w-6">
                  <TbAlertTriangleFilled size={24} className="h-6 w-6 text-iconPrompt" />
                </div>
                <div className="flex max-w-[250px] flex-col items-start gap-1 p-0">
                  <div className="text-sm font-medium text-iconPrompt">{t('deploy_error_tips')}</div>
                  <div className="max-w-[250px] break-words  text-xs font-normal text-iconPrompt sm:text-sm">
                    <a href={exploreUrl} target="_blank" className="cursor-pointer underline">
                      {t('click_tips')}
                    </a>
                  </div>
                </div>
              </div>
              <X
                size={20}
                className="cursor-pointer text-prompt200 hover:text-bgPrompt"
                onClick={() => toast.remove(to.id)}
                onTouchStart={() => toast.remove(to.id)}
              />
            </div>
          ),
          { position: isPhone ? 'bottom-center' : 'bottom-right', duration: 6000 }
        )
      } else {
        errorToast(errorInfo === 'User rejected the request.' ? tCommon('user_rejected_request') : errorInfo)
      }
    }
  }, [isError])
  const draftImage = useCallback(async () => {
    try {
      const result = await Promise.all(
        fileInfos.map(async (item) => {
          // if (item?.uploadUrl) {
          //   return { ...item }
          // }
          if (item?.file) {
            const nfturl = await uploadToCloudFlareForPump(item.file as File)
            return { ...item, uploadUrl: nfturl }
          } else if (item?.uploadUrl) {
            return { ...item }
          }
          // else if (saveCollection?.data?.logoImage || saveCollection?.data?.nftImage.length > 0) {
          //   if (item.type === 'logo') return { ...item, uploadUrl: saveCollection?.data?.logoImage }
          //   if (item.type === 'nft') return { ...item, uploadUrl: saveCollection?.data?.nftImage[0] }
          // }
          return { ...item, uploadUrl: '' }
        })
      )
      setFileInfos([...result])
      return result
    } catch (error: any) {
      errorToast(error?.msg || error?.message)
      setSaveLoading(false)
      return null
    }
  }, [fileInfos])
  const goBack = () => {
    lngPush('/pump404')
  }

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value

      if (inputValue.startsWith('-')) {
        setBasicInfo({ ...basicInfo, initBuy: '' })
        return
      }
      if (/^0\d+/.test(inputValue)) {
        setBasicInfo({ ...basicInfo, initBuy: '' })

        return
      }
      if (!/^\d*\.?\d*$/.test(inputValue)) {
        setBasicInfo({ ...basicInfo, initBuy: '' })

        return
      }
      try {
        const num = BigInt(inputValue.replace(/\.\d*$/, ''))
        const max = BigInt('1' + '0'.repeat(20))
        if (num > max) {
          // setBasicInfo({ ...basicInfo, initBuy: '' })
          return
        }
      } catch {
        // setBasicInfo({ ...basicInfo, initBuy: '' })
        return
      }
      // 检查小数点后是否超过18位，且非零数字不超过5位
      const result = checkNumberAndFormatZero(inputValue)
      if (!result) return
      setBasicInfo({ ...basicInfo, initBuy: inputValue })
    },
    [basicInfo]
  )
  const saveDraft = useCallback(
    withConnect(async () => {
      // if (vaildate('draft')) {
      setSubmitError(false)
      setSaveLoading(true)
      const uploadImage = await draftImage()
      const params: IPumpCreateparams = {
        dexName: 'fusion',
        name: basicInfo?.name?.replaceAll('"', '\\u0022') || '',
        symbol: basicInfo?.symbol?.replaceAll('"', '\\u0022') || '',
        description: basicInfo?.description?.replaceAll('"', '\\u0022') || '',
        contractAddress: '',
        chainName: chainInfo?.[getChainIdByBlockchain(blockchain) as IChain]?.name || '',
        chainId: getChainIdByBlockchain(blockchain) as IChain,
        tokenSupply: '10000000000',
        nftSupply: 10000,
        coefficient: 100000,
        initialBuy: basicInfo?.initBuy || '',
        logoImage: uploadImage?.find((item) => item.type == 'logo')?.uploadUrl || '',
        nftImage: uploadImage?.find((item) => item.type == 'nft')?.uploadUrl
          ? [uploadImage?.find((item) => item.type == 'nft')?.uploadUrl as string]
          : [],
        isDeployed: false,
        withExternalLink: showLinks,
        websiteUrl,
        twitterUrl,
        discordUrl,
        telegramUrl,
      }
      if (saveCollection?.data?.id && !saveCollection?.data?.isDeployed) {
        params.id = saveCollection?.data?.id
      }
      setIsSave(true)
      collectionCreate(params)
        .then((data) => {
          if (data?.code == 200) {
            successToast(t('save_success'))
            saveRefetch?.()
          } else {
            errorToast(data?.msg)
          }
          setSaveLoading(false)
        })
        .catch((err) => {
          setSaveLoading(false)
          errorToast(err)
        })
      // } else {
      //   setSubmitError(true)
      // }
    }),
    [
      // vaildate,
      draftImage,
      basicInfo?.name,
      basicInfo?.symbol,
      basicInfo?.description,
      basicInfo?.initBuy,
      blockchain,
      showLinks,
      websiteUrl,
      twitterUrl,
      discordUrl,
      telegramUrl,
      saveCollection?.data?.id,
      saveCollection?.data?.isDeployed,
      successToast,
      t,
      saveRefetch,
      withConnect,
    ]
  )
  return (
    <CreateTokenContext.Provider
      value={{
        basicinfo: basicInfo,
        setBasicInfo,
        fileInfos,
        setFileInfos,
        submitError,
        setSubmitError,
        showLinks,
        setShowLinks,
        websiteUrl,
        setWebsiteUrl,
        twitterUrl,
        setTwitterUrl,
        discordUrl,
        setDiscordUrl,
        telegramUrl,
        setTelegramUrl,
        isSave,
        setIsSave,
        blockchain,
        setBlockchain,
      }}
    >
      <div className="flex h-auto w-full flex-col items-center justify-center  py-6 text-sm md:px-7 lg:px-8">
        <div className="flex w-full flex-row items-center justify-between border-b-1 border-solid border-borderPrimary px-0 pb-3 pt-0 md:w-[720px] md:max-w-[720px]">
          <Button className="border-2 border-solid border-borderSecondary bg-baseWhite text-primary" onClick={goBack}>
            {t('create_back')}
          </Button>
          <div className="flex flex-row justify-end gap-3 p-0">
            <Button
              className="border-2 border-solid border-borderSecondary bg-baseWhite text-primary"
              onClick={saveDraft}
              disabled={saveLoading}
            >
              {t('save_draft')}
            </Button>
            <Button onClick={deployPump}>{t('deploy')}</Button>
          </div>
        </div>
        <div className="flex w-full flex-col items-center gap-6 px-0 pb-0 pt-6">
          {/* title */}
          <div className="flex w-full flex-col items-start gap-5 rounded-2xl px-6 py-0 md:isolate md:w-[720px] md:max-w-[720px]">
            <div className="flex flex-row flex-wrap content-start items-center justify-between gap-x-6 gap-y-4 p-0">
              {titleArr.map((item, index) => (
                <TitleComp {...item} key={`title_comp_${index.toString()}`} />
              ))}
            </div>
          </div>
          {/* blockchain */}
          <BlockChainForm />
          {/* basic */}
          <BasicForm />
          {/* upload image */}
          <UploadForm />
          {/* Initial Buy */}
          <div className="flex w-full flex-row items-start gap-5 rounded-2xl border-1 border-solid border-borderPrimary px-6 py-5 md:isolate md:w-[720px] md:max-w-[720px]">
            <div className="flex w-full flex-col items-start gap-1 p-0">
              <div className="flex flex-row items-center gap-0.5 p-0 text-primary">
                {t('initial_buy')}
                {/* <div>*</div> */}
                <NftTooltip content={t('init_buy_tips')}>
                  <CircleHelp size={16} className=" cursor-pointer" />
                </NftTooltip>
              </div>
              <Input
                className={twMerge(
                  'w-full text-start',
                  basicInfo?.initBuy &&
                    Number(basicInfo?.initBuy) > 0 &&
                    (Number(basicInfo?.initBuy) < decimalInfo[getChainCurrency(chainId) || 'ALG'] ||
                      !(Number(formatEther((tokenAmount as bigint) || BigInt(0))) > 0)) &&
                    'bg-buttonError'
                )}
                inputClassName="text-start"
                placeholder={t('Price')}
                suffix={
                  <div className="text-sm font-medium text-quaternary">
                    {getChainCurrency(getChainIdByBlockchain(blockchain) as IChain)}
                  </div>
                }
                value={basicInfo?.initBuy}
                onChange={handleInputChange}
              />
              {basicInfo?.initBuy &&
                Number(basicInfo?.initBuy) > 0 &&
                (Number(basicInfo?.initBuy) < decimalInfo[getChainCurrency(chainId) || 'ALG'] ||
                  !(Number(formatEther((tokenAmount as bigint) || BigInt(0))) > 0)) && (
                  <div className="text-sm font-normal text-error">{t('tokenAmount_tips')}</div>
                )}
            </div>
          </div>
          {/* links */}
          <Links />
          {/* Zapry */}
          {/* <ZapryCommunity /> */}
          <FollowSteps
            isOpen={isOpen}
            onClose={onClose}
            onOpenChange={onOpenChange}
            steps={steps}
            currentStep={currentStep}
          />
        </div>
      </div>
    </CreateTokenContext.Provider>
  )
}

export default memo(CreateToken)
