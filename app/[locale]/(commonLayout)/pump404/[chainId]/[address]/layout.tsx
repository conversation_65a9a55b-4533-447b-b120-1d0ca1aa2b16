import React from 'react'

import { Metada<PERSON> } from 'next'

import { chainInfo } from '@/constant/chainInfo'
import { IResponse } from '@/request/type/response'
import { <PERSON><PERSON>hai<PERSON> } from '@/types/IChain'
import { IPumpToken } from '@/types/IPump'

// import { IHomeCollectionData } from '@/types/ITable'
// const graphApi = process.env.NEXT_PUBLIC_GRAPH_URL
const fusionUrl = process.env.NEXT_PUBLIC_FUSION_HREF
// const algChain = process.env.NEXT_PUBLIC_ALG_CHAIN
const reqApi = process.env.NEXT_PUBLIC_REQUEST_API
interface Props {
  params: { address: string; locale: string; chainId: string }
}
const toUnicodeSubscript = (count: number): string => {
  const subscriptDigits = '₀₁₂₃₄₅₆₇₈₉'
  return count
    .toString()
    .split('')
    .map((digit) => subscriptDigits[parseInt(digit, 10)])
    .join('')
}

const formatNumberWithUnicodeSubscript = (number: number | string, isfix?: boolean): string => {
  const numStr = typeof number === 'number' ? number.toString() : number

  if (isNaN(Number(numStr)) || numStr === '0') {
    return '0'
  }
  const normalizedNumStr = numStr.includes('e') ? Number(numStr).toFixed(21) : numStr

  const precision = 2

  const [integerPart, decimalPart = ''] = normalizedNumStr.split('.')

  if (!decimalPart) {
    return integerPart
  }

  const match = decimalPart.match(/^(0+)/)
  const zeroCount = match ? match[1].length : 0

  const significantPart = decimalPart.slice(zeroCount, zeroCount + precision).replace(/0+$/, '')
  if (isfix) {
    return `${integerPart}.${decimalPart.slice(0, zeroCount + 4)}`
  }
  if (zeroCount >= 2) {
    return `${integerPart}.0${toUnicodeSubscript(zeroCount)}${significantPart}`
  } else {
    return `${integerPart}.${decimalPart.slice(0, precision)}`
  }
}
const formatPrice = (number: number | string, isfix?: boolean) => {
  let num = number
  if (typeof num === 'string') {
    num = Number(num)
  }
  if (isNaN(num)) {
    return 0
  }
  if (num === 0) {
    return 0
  }
  num = formatNumberWithUnicodeSubscript(num, isfix)
  return num
}
const language = (
  locale: string,
  price: string,
  name: string,
  symbol: string,
  chainSymbol: string,
  chainName: string
) => {
  switch (locale) {
    case 'en':
      return `$${price} ${name} (${symbol}) realtime price charts, trading history and info - ${symbol} / ${chainSymbol} on ${chainName} / Fusion Pump404`
    case 'ja-JP':
      return `$${price} ${name} (${symbol}) リアルタイム価格チャート、取引履歴、情報 - ${symbol} / ${chainSymbol} on ${chainName} / Fusion Pump404`
    case 'ko-KR':
      return `$${price} ${name} (${symbol}) 실시간 가격 차트, 거래 내역 및 정보 - ${symbol} / ${chainSymbol} on ${chainName} / Fusion Pump404`
    case 'zh-CN':
      return `$${price} ${name} (${symbol}) 实时价格图表、交易历史和信息 - ${symbol} / ${chainSymbol} 在 ${chainName} / Fusion Pump404`
    case 'zh-TW':
      return `$${price} ${name} (${symbol}) 即時價格圖表、交易歷史與資訊 - ${symbol} / ${chainSymbol} 在 ${chainName} / Fusion Pump404`
    case 'mn-MN':
      return `$${price} ${name} (${symbol}) бодит цагийн үнийн график, арилжааны түүх, мэдээлэл - ${chainName} / Fusion Pump404 дээрх ${symbol} / ${chainSymbol}`
    case 'id-ID':
      return `Grafik harga realtime $${price} ${name} (${symbol}), riwayat perdagangan dan info - ${symbol} / ${chainSymbol} pada ${chainName} / Fusion Pump404`
    case 'th-TH':
      return `$${price} ${name} (${symbol}) แผนภูมิราคาแบบเรียลไทม์ ประวัติการซื้อขายและข้อมูล - ${symbol} / ${chainSymbol} บน ${chainName} / Fusion Pump404`
    case 'tr-TR':
      return `$${price} ${name} (${symbol}) gerçek zamanlı fiyat grafikleri, işlem geçmişi ve bilgileri - ${symbol} / ${chainSymbol} ${chainName} / Fusion Pump404 üzerinde`
    case 'vi-VN':
      return `Biểu đồ giá theo thời gian thực, lịch sử giao dịch và thông tin của $${price} ${name} (${symbol}) - ${symbol} / ${chainSymbol} trên ${chainName} / Fusion Pump404`
  }
}
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { address, locale, chainId } = params

  if (address) {
    try {
      const pumpCollection = await fetch(`${reqApi}/collection/find?chainId=${chainId}&&contractAddress=${address}}`, {
        cache: 'no-store',
      })
      console.log('paramsDetail:', params)
      const data = (await pumpCollection.json()) as IResponse<IPumpToken>
      if (data?.code == 200) {
        const pumpData = data?.data
        console.log('pumpData:', pumpData)
        console.log('priceInfo:', (Number(pumpData?.marketCap) / Number(pumpData?.tokenSupply)).toString())
        const price = formatPrice((Number(pumpData?.marketCap) / Number(pumpData?.tokenSupply)).toString())
        console.log('price:', price)
        const fixPrice = formatPrice((Number(pumpData?.marketCap) / Number(pumpData?.tokenSupply)).toString(), true)
        const desc = language(
          locale,
          fixPrice.toString(),
          pumpData?.name,
          pumpData?.symbol,
          chainInfo[pumpData?.chainId as keyof typeof chainInfo]?.symbol,
          chainInfo[pumpData?.chainId as keyof typeof chainInfo]?.name
        )
        return {
          title: `${pumpData?.symbol} $${price} - ${pumpData?.name} / ${chainInfo?.[pumpData?.chainId as IChain]?.symbol} on ${chainInfo?.[pumpData?.chainId as IChain]?.name} - Fusion Pump404`,
          metadataBase: new URL(fusionUrl as string),
          description: `${desc}`,
          authors: [
            {
              name: `@${pumpData?.user?.displayName}`,
            },
          ],
          openGraph: {
            description: `${desc}`,
            title: `${pumpData?.symbol} $${price} - ${pumpData?.name} / ${chainInfo?.[pumpData?.chainId as IChain]?.symbol} on ${chainInfo?.[pumpData?.chainId as IChain]?.name} - Fusion Pump404`,
            images: [
              {
                url: pumpData?.logoImage as string,
                alt: `${pumpData?.name} / ${chainInfo?.[pumpData?.chainId as IChain]?.name}`,
              },
            ],
            creators: `@${pumpData?.user?.displayName}`,
            siteName: 'Fusion Pump404',
          },
          twitter: {
            description: `${desc}`,
            site: 'Fusion Pump404',
            title: `${pumpData?.symbol} $${price} - ${pumpData?.name} / ${chainInfo?.[pumpData?.chainId as IChain]?.symbol} on ${chainInfo?.[pumpData?.chainId as IChain]?.name} - Fusion Pump404`,
            images: [
              {
                url: pumpData?.logoImage as string,
                alt: `${pumpData?.name} / ${chainInfo?.[pumpData?.chainId as IChain]?.name}`,
              },
            ],
            creator: `@${pumpData?.user?.displayName}`,
          },
        }
      }
    } catch (error) {
      console.log('user-profile-error:', error)
    }
  }

  return {
    title: 'Fusion Pump404',
  }
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return <>{children}</>
}
