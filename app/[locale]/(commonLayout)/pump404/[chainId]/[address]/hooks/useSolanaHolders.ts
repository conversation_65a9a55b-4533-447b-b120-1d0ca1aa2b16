import { useCallback } from 'react'

import { useInfiniteQuery, useQuery } from '@tanstack/react-query'

import { formatBn } from '@/libs/common/format'

interface TokenAccount {
  address: string
  mint: string
  owner: string
  amount: number
  delegated_amount: number
  frozen: boolean
}

interface SolanaHoldersResponse {
  result?: {
    token_accounts: TokenAccount[]
  }
}
interface PageData {
  holders: any[]
  nextPage: number | null
}

interface UseSolanaHoldersProps {
  mintAddress: string
  excludeAddresses?: string[]
  enabled?: boolean
}

interface UseSolanaHoldersReturn {
  holders: any[]
  isLoading: boolean
  hasMore: boolean
  isSuccess: boolean
  loadMore: () => Promise<void>
  refetch: () => Promise<void>
  totalHolders: number
  isTotalHoldersLoading: boolean
  isTotalHoldersSuccess: boolean
  refetchTotalHolders: () => Promise<void>
}

async function fetchHoldersPage(
  mintAddress: string,
  pageParam: number,
  excludeAddresses: string[] = []
): Promise<{ holders: TokenAccount[]; hasMore: boolean }> {
  if (!mintAddress) return { holders: [], hasMore: false }

  try {
    const rpcUrl = process.env.NEXT_PUBLIC_HELIUS_RPC_URL
    const apiKey = process.env.NEXT_PUBLIC_HELIUS_API_KEY
    const url = `${rpcUrl}/?api-key=${apiKey}`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'getTokenAccounts',
        id: 'helius-test',
        params: {
          page: pageParam,
          limit: 1000,
          displayOptions: {},
          mint: mintAddress,
        },
      }),
    })

    if (!response.ok) {
      console.error(`Error: ${response.status}, ${response.statusText}`)
      return { holders: [], hasMore: false }
    }

    const data: SolanaHoldersResponse = await response.json()

    if (!data.result || data.result.token_accounts.length === 0) {
      return { holders: [], hasMore: false }
    }

    const filteredAccounts = data.result.token_accounts.filter((account) => !excludeAddresses.includes(account.owner))

    const hasMore = data.result.token_accounts.length === 1000

    return {
      holders: filteredAccounts,
      hasMore,
    }
  } catch (error) {
    console.error('Error fetching Solana holders:', error)
    return { holders: [], hasMore: false }
  }
}

export async function fetchAllHoldersCount(mintAddress: string, excludeAddresses: string[] = []): Promise<number> {
  if (!mintAddress) return 0

  let totalCount = 0
  let currentPage = 1
  let hasMore = true

  while (hasMore) {
    try {
      const { holders, hasMore: morePages } = await fetchHoldersPage(mintAddress, currentPage, excludeAddresses)
      totalCount += holders.length
      hasMore = morePages
      currentPage++

      if (currentPage > 50) {
        console.warn('Reached maximum page limit, stopping fetching holders count')
        break
      }
    } catch (error) {
      console.error('Error fetching all holders count:', error)
      break
    }
  }

  return totalCount
}

export const useSolanaHolders = ({
  mintAddress,
  excludeAddresses = [],
  enabled = true,
}: UseSolanaHoldersProps): UseSolanaHoldersReturn => {
  const { data, isLoading, fetchNextPage, hasNextPage, refetch, isFetchingNextPage, isSuccess } =
    useInfiniteQuery<PageData>({
      queryKey: ['solanaHolders', mintAddress, excludeAddresses],
      queryFn: async ({ pageParam = 1 }: { pageParam: any }) => {
        if (!mintAddress) return { holders: [], nextPage: null }

        try {
          const rpcUrl = process.env.NEXT_PUBLIC_HELIUS_RPC_URL
          const apiKey = process.env.NEXT_PUBLIC_HELIUS_API_KEY
          const url = `${rpcUrl}/?api-key=${apiKey}`

          const response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              jsonrpc: '2.0',
              method: 'getTokenAccounts',
              id: 'helius-test',
              params: {
                page: pageParam,
                limit: 10,
                displayOptions: {},
                mint: mintAddress,
              },
            }),
          })

          if (!response.ok) {
            console.error(`Error: ${response.status}, ${response.statusText}`)
            return { holders: [], nextPage: null }
          }

          const data: SolanaHoldersResponse = await response.json()

          if (!data.result || data.result.token_accounts.length === 0) {
            return { holders: [], nextPage: null }
          }

          const filteredAccounts = data.result.token_accounts
            .filter((account) => !excludeAddresses.includes(account.owner))
            .sort((a, b) => Number(b.amount) - Number(a.amount))

          const formattedHolders = filteredAccounts.map((account) => ({
            erc404TokenAddr: account.mint,
            ownerId: account.owner,
            balance: formatBn(account.amount.toString()),
          }))

          const hasNextPage = data.result.token_accounts.length === 10
          const nextPage = hasNextPage ? pageParam + 1 : null

          return {
            holders: formattedHolders,
            nextPage,
          }
        } catch (error) {
          console.error('Error fetching Solana holders:', error)
          return { holders: [], nextPage: null }
        }
      },
      initialPageParam: 1,
      getNextPageParam: (lastPage) => lastPage.nextPage,
      enabled: enabled && !!mintAddress,
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
    })

  const {
    data: totalHoldersData,
    isLoading: isTotalHoldersLoading,
    isSuccess: isTotalHoldersSuccess,
    refetch: refetchTotalHolders,
  } = useQuery({
    queryKey: ['solanaHoldersCount', mintAddress, excludeAddresses],
    queryFn: async () => {
      return await fetchAllHoldersCount(mintAddress, excludeAddresses)
    },
    enabled: enabled && !!mintAddress,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })

  const holders = data?.pages.reduce<any[]>((acc, page) => [...acc, ...page.holders], []) || []

  const loadMore = useCallback(async () => {
    if (!isLoading && !isFetchingNextPage && hasNextPage) {
      await fetchNextPage()
    }
  }, [fetchNextPage, hasNextPage, isLoading, isFetchingNextPage])

  return {
    holders,
    isLoading: isLoading || isFetchingNextPage,
    hasMore: !!hasNextPage,
    isSuccess,
    loadMore,
    refetch: async () => {
      await refetch()
    },
    totalHolders: totalHoldersData || 0,
    isTotalHoldersLoading,
    isTotalHoldersSuccess,
    refetchTotalHolders: async () => {
      await refetchTotalHolders()
    },
  }
}
