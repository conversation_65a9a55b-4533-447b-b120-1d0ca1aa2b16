import React from 'react'

import { Metada<PERSON> } from 'next'
import { redirect } from 'next/navigation'

import { isSolanaChain } from '@/hooks/useSolanaInfo'
import { IResponse } from '@/request/type/response'
import { IChain } from '@/types/IChain'
import { IHomeCollectionData } from '@/types/ITable'

const fusionApi = process.env.NEXT_PUBLIC_REQUEST_API
const fusionUrl = process.env.NEXT_PUBLIC_FUSION_HREF
const algChain = process.env.NEXT_PUBLIC_ALG_CHAIN
interface Props {
  params: { address: string; locale: string; chainId: IChain }
}
type IMetaData = {
  [key: string]: {
    title: string
    description: string
  }
}
const mainMetaData: IMetaData = {
  en: {
    title: 'Fusion - The Quantum Leap for NFTs',
    description:
      'Fusion is a revolutionary platform built upon the Quantum NFT Minting Theory, perceiving NFTs and meme coins as two sides of the same particle. Utilizing the principles of quantum mechanics, Fusion redefines the creation and exchange of digital assets. Its Forge acts as a wormhole, connecting different parallel spaces and bestowing each NFT with a unique quantum signature.  Within Fusion, you can forge quantum NFTs, transforming your creativity into digital assets with boundless potential.',
  },
  'ja-JP': {
    description:
      'Fusionは、量子NFTミント理論に基づいて構築された画期的なプラットフォームであり、NFTとミームコインを同一粒子の異なる側面と捉えています。量子力学の原理を活用することで、Fusionはデジタル資産の創造と交換を再定義します。そのForgeはワームホールとして機能し、異なる並行空間を接続し、各NFTに独自の量子署名を付与します。Fusionでは、量子NFTを鍛造し、あなたの創造性を無限の可能性を秘めたデジタル資産に変換することができます。',
    title: 'Fusion - NFT の量子飛躍',
  },
  'ko-KR': {
    description:
      'Fusion은 양자 NFT 민팅 이론을 기반으로 구축된 혁신적인 플랫폼으로, NFT와 밈 코인을 동일한 입자의 두 측면으로 인식합니다. 양자 역학의 원리를 활용하여 Fusion은 디지털 자산의 생성과 교환을 재정의합니다. Fusion의 Forge는 웜홀 역할을 하여 다른 평행 우주를 연결하고 각 NFT에 고유한 양자 서명을 부여합니다. Fusion에서 당신은 양자 NFT를 만들어 창의성을 무한한 가능성을 가진 디지털 자산으로 변환할 수 있습니다.',
    title: 'Fusion - NFT 의 양자 도약',
  },
  'zh-CN': {
    description:
      'Fusion 是一个基于量子 NFT 铸造理论构建的革命性平台，它将 NFT 和 Meme 币视为一体两面的粒子，并利用量子力学原理重新定义数字资产的创造和交易。Fusion  的铸造间犹如一个虫洞，连接着不同的平行空间，赋予每个 NFT 独一无二的量子标记。在 Fusion 平台上，你可以铸造量子 NFT，将你的创意转化为具有无限潜力的数字资产。',
    title: 'Fusion - NFT 的量子跃迁',
  },
  'zh-TW': {
    description:
      'Fusion 是一個基於量子 NFT 鑄造理論構建的革命性平台，它將 NFT 和 Meme 幣視為一體兩面的粒子，並利用量子力學原理重新定義數位資產的創造和交易。 Fusion  的鑄造間猶如一個蟲洞，連接著不同的平行空間，賦予每個 NFT 獨一無二的量子標記。 在 Fusion 平台上，你可以鑄造量子 NFT，將你的創意轉化為具有無限潛力的數位資產。',
    title: 'Fusion - NFT 的量子躍遷',
  },
  'mn-MN': {
    description:
      'Fusion бол NFT болон Meme зоосыг нэг бие дэх хоёр талт бөөмс гэж үздэг квант NFT цутгах онол дээр суурилсан хувьсгалт платформ бөгөөд дижитал хөрөнгийг бий болгох, арилжаалах үйл явцыг дахин тодорхойлоход квант механикийн зарчмуудыг ашигладаг. Fusion-ийн цутгах өрөө нь өөр өөр зэрэгцээ орон зайг холбосон өтний нүхтэй адил бөгөөд NFT бүрд өвөрмөц квант гарын үсэг өгдөг. Fusion платформ дээр та квант NFT-ийг гаргаж, санаагаа хязгааргүй боломжит дижитал хөрөнгө болгон хувиргаж чадна.',
    title: 'Fusion - NFT-ийн квант үсрэлт',
  },
  'vi-VN': {
    description:
      'Fusion là một nền tảng cách mạng được xây dựng dựa trên Lý thuyết Đúc NFT Lượng Tử, coi NFT và meme coin là hai mặt của cùng một hạt. Bằng cách áp dụng các nguyên lý của cơ học lượng tử, Fusion tái định nghĩa việc tạo ra và trao đổi tài sản kỹ thuật số. Forge của Fusion hoạt động như một lỗ sâu, kết nối các không gian song song khác nhau và trao cho mỗi NFT một chữ ký lượng tử độc nhất. Trong Fusion, bạn có thể đúc NFT lượng tử, biến sự sáng tạo của mình thành tài sản kỹ thuật số với tiềm năng vô hạn.',
    title: 'Fusion - Bước Nhảy Lượng Tử Cho NFT',
  },
  'id-ID': {
    description:
      'Fusion adalah platform revolusioner yang dibangun di atas Teori Pencetakan NFT Kuantum, memandang NFT dan koin meme sebagai dua sisi dari partikel yang sama. Dengan memanfaatkan prinsip mekanika kuantum, Fusion mendefinisikan ulang penciptaan dan pertukaran aset digital. Forge-nya bertindak sebagai lubang cacing, menghubungkan berbagai ruang paralel dan memberikan setiap NFT tanda kuantum yang unik. Dalam Fusion, Anda dapat mencetak NFT kuantum, mengubah kreativitas Anda menjadi aset digital dengan potensi tak terbatas.',
    title: 'Fusion - Lompatan Kuantum untuk NFT',
  },
  'th-TH': {
    description:
      'Fusion เป็นแพลตฟอร์มปฏิวัติที่สร้างขึ้นบนทฤษฎีการสร้าง NFT เชิงควอนตัม โดยมองว่า NFT และเหรียญมีมเป็นสองด้านของอนุภาคเดียวกัน ด้วยการใช้หลักการของกลศาสตร์ควอนตัม Fusion ได้กำหนดนิยามใหม่ในการสร้างและแลกเปลี่ยนสินทรัพย์ดิจิทัล Forge ของมันทำหน้าที่เป็นรูหนอน เชื่อมต่อพื้นที่คู่ขนานต่างๆ และมอบลายเซ็นควอนตัมเฉพาะตัวให้กับ NFT ทุกชิ้น ใน Fusion คุณสามารถสร้าง NFT เชิงควอนตัม เปลี่ยนความคิดสร้างสรรค์ของคุณให้กลายเป็นสินทรัพย์ดิจิทัลที่มีศักยภาพไร้ขีดจำกัด',
    title: 'Fusion - ก้าวกระโดดเชิงควอนตัมสำหรับ NFT',
  },
  'tr-TR': {
    description: `Fusion, Kuantum NFT Basım Teorisi üzerine inşa edilmiş devrim niteliğinde bir platformdur ve NFT'leri ile meme coin'leri aynı parçacığın iki yüzü olarak görür. Kuantum mekaniği ilkelerini kullanarak, Fusion dijital varlıkların yaratımını ve değişimini yeniden tanımlar. Forge, farklı paralel alanları birbirine bağlayan bir solucan deliği gibi davranır ve her NFT'ye benzersiz bir kuantum imzası kazandırır. Fusion içerisinde, kuantum NFT'ler oluşturabilir ve yaratıcılığınızı sınırsız potansiyele sahip dijital varlıklara dönüştürebilirsiniz.`,
    title: `Fusion - Kuantum Sıçraması NFT'ler İçin`,
  },
}
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { address, locale, chainId } = params
  const language = (name: string) => {
    switch (locale) {
      case 'en':
        return `${name} NFTs on Fusion: Buy, Sell and Trade | Fusion`
      case 'ja-JP':
        return `Fusionでの${name} NFT：購入、販売、取引 | Fusion`
      case 'ko-KR':
        return `Fusion에서의 ${name} NFT: 구매, 판매 및 거래 | Fusion`
      case 'zh-CN':
        return `Fusion 上的 ${name} NFT：购买、出售和交易 | Fusion`
      case 'zh-TW':
        return `Fusion 上的 ${name} NFT：購買、出售和交易 | Fusion`
      case 'mn-MN':
        return `Fusion дээрх ${name} NFT: Худалдах, зарах болон арилжаалах | Fusion`
      case 'id-ID':
        return `NFT ${name} di Fusion: Pembelian, Penjualan, dan Perdagangan | Fusion`
      case 'th-TH':
        return `NFT ${name} ใน Fusion: การซื้อ, การขาย, และการแลกเปลี่ยน | Fusion`
      case 'tr-TR':
        return `Fusion'da ${name} NFT: Satın Alma, Satış ve Takas | Fusion`
      case 'vi-VN':
        return `NFT ${name} trên Fusion: Mua, Bán và Giao Dịch | Fusion`
    }
  }
  if (address && chainId) {
    try {
      const response = await fetch(
        `${fusionApi}/home/<USER>
      )
      console.log('response', response)
      const data = (await response.json()) as IResponse<IHomeCollectionData>
      if (data?.code == 200) {
        const collection = data?.data
        return {
          title: `${language(collection?.name as string)}`,
          metadataBase: new URL(fusionUrl as string),
          authors: [
            {
              name: `Created by ${collection?.name}`,
            },
          ],
          openGraph: {
            title: `${language(collection?.name as string)}`,
            images: [
              {
                url: collection?.cover as string,
                alt: collection?.name,
              },
            ],
            creators: `Created by ${collection?.name}`,
            siteName: mainMetaData[locale].title,
          },
          twitter: {
            title: `${language(collection?.name as string)}`,
            images: [
              {
                url: collection?.cover as string,
                alt: collection?.name,
              },
            ],
            creator: `Created by ${collection?.name}`,
          },
        }
      }
    } catch (error) {
      console.log('user-profile-error:', error)
    }
  }

  return {
    title: 'Fusion',
  }
}

export default function Layout({
  children,
  params,
}: {
  children: React.ReactNode
  params: { address: string; locale: string; chainId: IChain }
}) {
  const { address, chainId } = params
  if (chainId && chainId?.toString()?.startsWith('0x')) {
    redirect(`/collection/${algChain}/${chainId}/${address}`) // serve redirect
  }
  return <>{children}</>
}
