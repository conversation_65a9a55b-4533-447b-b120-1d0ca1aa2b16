import React, { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { useInView } from 'react-intersection-observer'

// import { useApolloClient } from '@apollo/client'
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query'
import dayjs from 'dayjs'
// import { Contract, JsonRpcProvider, Overrides } from 'ethers'
import { twMerge } from 'tailwind-merge'
import { useDebounceCallback, useMediaQuery } from 'usehooks-ts'
import { formatEther, parseEther } from 'viem'

import EmptyComp from '@/components/EmptyComp'
import NftFilterPanel, { IFilterPanel } from '@/components/Filters/FilterPanel'
import NftFilters from '@/components/Filters/NftFilters'
import CardListSkeleton from '@/components/library/Skeleton/CardListSkeleton'
import CardSkeleton from '@/components/library/Skeleton/CardSkeleton'
import NftCard, { INftCard } from '@/components/NftCard'

// import useCollectionList2 from '@/components/TrendCard/hooks/useCollectionList2'
// import useGetCollectionList from '../../hooks/useGetCollectionList'
// import useCollectionList2 from '@/components/TrendCard/hooks/useCollectionList2'
import { FilterContext, IFilterContext } from '../../../../../../../../components/Filters/FilterContext'
// import supportCollections from '@/constant/collection'
import { SearchNftQueryQueryVariables } from '@/graphql/generated'
import { GraphContext } from '@/graphql/GraphContext'
import useElectionAddress from '@/hooks/contract/address/useElectionAddress'
import { deserializeAttrs, serializeAttrs } from '@/hooks/useCollectionPersistence'
import { useReadNftSearchParams, useUpdateNftSearchParams } from '@/hooks/useUpdateSearchParams'
// import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
// import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
// import useAlgRpc from '@/hooks/useAlgRpc'
import { formatTokenId, getChangeChainId } from '@/libs/common/format'
// import { electionContractConfig } from '@/libs/contract/contractConfig'
import { supportChainId } from '@/libs/contract/wagmiConf'
import { ponder_collectionNft } from '@/libs/graph'
import { IGraphTokenQuery } from '@/libs/graph/buildGraph'
import { toLowerCaseLettersOnly } from '@/libs/utils/util'
import { getCollectionNfts } from '@/request/api/collection'
// import fetchMetadata from '@/request/api/metadata'
import { tokenSearch as tokenSearchReq } from '@/request/api/nft'
// import { INftItem } from '@/types/INftItem'
import { IHomeCollectionData } from '@/types/ITable'

// import { INftItem } from '@/types/INftItem'

const filterOptions: IFilterPanel[] = ['status', 'price', 'properties']
const NftList = ({ collectionItem }: { collectionItem: IHomeCollectionData }) => {
  const updateSearchParams = useUpdateNftSearchParams('nfts')
  const readSearchParams = useReadNftSearchParams('nfts')
  const urlParams = readSearchParams()
  const isPhone = useMediaQuery('(max-width: 768px)')
  const isPad = useMediaQuery('(max-width: 1024px)')
  const { address: contractAddress } = collectionItem
  // const algRpc = useAlgRpc()
  console.log('collectionitem', collectionItem)
  // const fusion = useFusionAddress()
  const election = useElectionAddress()
  const isFusion = useMemo(() => supportChainId == collectionItem.chainId, [collectionItem])
  const queryClient = useQueryClient()
  // const client = useApolloClient()
  const { ref, inView } = useInView({
    threshold: 1, // Trigger when fully visible
  })
  const [isSmallCard, setIsSmallCard] = useState<boolean>(urlParams.isSmall || false)
  const [isFilter, setIsFilter] = useState<boolean>(false)
  const [selectedDropdown, setSelectedDropdown] = useState<string>(urlParams.selectedDropdown || 'trending')
  const [filterParams, setFilterParams] = useState<any>(() => {
    const initialFilterParams: any = { status: 'all' }

    if (urlParams.price_gte && urlParams.priceEndTime_gt) {
      initialFilterParams.status = 'buy'
    }
    if (urlParams.price_lte === '0' && urlParams.priceEndTime_lt) {
      initialFilterParams.status = 'noSale'
    }
    if (urlParams.price_lte || urlParams.price_gte) {
      initialFilterParams.price = {
        minValue: urlParams.price_gte ? formatEther(BigInt(urlParams.price_gte)) : '',
        maxValue:
          urlParams.price_lte && initialFilterParams.status !== 'noSale'
            ? formatEther(BigInt(urlParams.price_lte))
            : '',
        symbol: 'ALG',
      }
    }

    if (urlParams.attrs) {
      initialFilterParams.attrs = deserializeAttrs(urlParams.attrs)
    } else {
      initialFilterParams.attrs = []
    }

    return initialFilterParams
  })
  const [tokenSearchParams, setTokenSearchParams] = useState<IGraphTokenQuery | null>(null)
  const [, setTokens] = useState<any[]>([])
  const filterContext: IFilterContext = useMemo(
    () => ({
      refreshNftSize: setIsSmallCard,
      smallNftCard: isSmallCard,
      changeFilter: setIsFilter,
      isFilter,
      filterParams,
      changeFilterParams: setFilterParams,
      graphParams: tokenSearchParams,
      changeGraphParams: (params: IGraphTokenQuery) => setTokenSearchParams({ ...params }),
      setTokens: (params: any[]) => setTokens(params),
    }),
    [isSmallCard, isFilter, filterParams, tokenSearchParams]
  )
  // const param = useParams()
  // const { isLoading, tokenList } = useGetCollectionList()
  // const { nftList: fetchNftList } = useCollectionList2(collectionItem?.contractAddress, collectionItem?.collectionName)
  // const [tokenList, setTokenList] = useState<INftItem[]>([])
  const [variables, setVariables] = useState<SearchNftQueryQueryVariables>(() => {
    return {
      orderBy: urlParams.orderBy || 'level',
      orderDirection: urlParams.orderDirection || 'desc',
      ...urlParams,
    }
  })
  const [attrs, setAttrs] = useState<any[]>(() => {
    const attrData = (urlParams.attrs && deserializeAttrs(urlParams.attrs)) || []
    return attrData
  })
  // const fusion = useFusionAddress()
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, refetch }: any = useInfiniteQuery({
    queryKey: ['collectionNfts', contractAddress, variables, attrs],
    queryFn: async ({ pageParam = null }) => {
      if (isFusion) {
        const params: any = {
          query: ponder_collectionNft,
          variables: {
            orderBy: 'level',
            orderDirection: 'desc',
            ...variables,
            after: pageParam || null,
            limit: 20,
            contractAddress: toLowerCaseLettersOnly(collectionItem.address),
          },
          operationName: 'SearchNftQuery',
          attrs: attrs,
          contractAddress: toLowerCaseLettersOnly(collectionItem.address),
        }
        if (!params.attrs || params.attrs.length === 0) {
          delete params.attrs
        }
        if (params.attrs && params.attrs.length > 0) {
          params.contractAddress = toLowerCaseLettersOnly(filterParams.collection?.contractAddress)
        }
        const { data } = await tokenSearchReq({ ...params })
        const { tokens, pageInfo } = data
        // if (election === collectionItem.contractAddress) {
        //   const customProvider = new JsonRpcProvider(algRpc)
        //   const contract = new Contract(electionContractConfig.address, electionContractConfig.abi, customProvider)
        //   const calls = tokens.map((item: { tokenId: any | Overrides }) => contract.tokenURI(item.tokenId))
        //   const result = await Promise.all(calls)
        //   const formteUrls = tokens.map((item: { tokenId: any }, index: number) => {
        //     return {
        //       result: result[index],
        //       tokenId: item.tokenId,
        //     }
        //   })
        //   const tokenIdsPromise = formteUrls.map((item: { result: string; tokenId: string }) =>
        //     fetchMetadata(item.result, item.tokenId)
        //   )
        //   const results = await Promise.all(tokenIdsPromise)
        //   // return results.map((item: any) => {
        //   //   return {
        //   //     tokenId: item.tokenId,
        //   //     metadata: { ...item },
        //   //   }
        //   // })
        //   const newItems = results.map((item: any) => {
        //     return {
        //       cover: item?.image || '',
        //       collectionName: collectionItem.collectionName,
        //       name: `${item.name} #${formatTokenId(item.tokenId)}`,
        //       // name: `Rabbit #${item.tokenId.toString().slice(0, 5)}`,
        //       id: item.tokenId,
        //       collectionAddress: collectionItem.contractAddress,
        //       price: 0.1,
        //       highestBid: 0,
        //       url: item.tokenURI,
        //       iframe_url: item?.iframe_url || '',
        //       nftDeatil: {
        //         ownerAddress: item?.ownerId,
        //         price: item?.price,
        //         priceEndTime: item?.priceEndTime,
        //         priceStartTime: item?.priceStartTime,
        //         offers: item?.offers?.items || [],
        //         chainId: item?.chainId,
        //       },
        //     } as INftCard
        //   })
        //   return { items: newItems, pageInfo }
        // }
        const newItems = tokens.map((item: any) => {
          const { attributes = [] } = item
          let level = item?.level || ''
          attributes?.map((item: { trait_type: string; value: any }) => {
            if (item?.trait_type == 'Level') {
              level = item?.value
            }
          })
          return {
            cover: item?.image || '',
            collectionName: collectionItem?.name,
            name:
              contractAddress == election
                ? `${item?.name} #${formatTokenId(item.tokenId)}`
                : `Rabbit #${formatTokenId(item.tokenId)}`,
            // name: `Rabbit #${item.tokenId.toString().slice(0, 5)}`,
            id: item.tokenId,
            collectionAddress: item?.contractAddress || '',
            price: 0.1,
            highestBid: 0,
            url: item.tokenURI,
            iframe_url: item?.iframe_url || '',
            nftDeatil: {
              ownerAddress: item?.ownerId,
              price: item?.price,
              priceEndTime: item?.priceEndTime,
              priceStartTime: item?.priceStartTime,
              offers: item?.offers?.items || [],
              chainId: item?.chainId,
            },
            level: level ? (level?.toString()?.startsWith('Lv') ? level : `Lv-${level}`) : '',
          } as INftCard
        })
        return { items: newItems, pageInfo }
      }
      const openseaNfts = await getCollectionNfts(
        collectionItem.address,
        getChangeChainId(collectionItem.chainId),
        20,
        pageParam || ''
      )
      const openseaData = (await openseaNfts.json()) as any
      const { nfts = [] } = openseaData
      const newItems = nfts.map((item: any) => {
        return {
          cover: item?.image_url || '',
          collectionName: collectionItem?.name,
          name: item?.name || '',
          // name: `Rabbit #${item.tokenId.toString().slice(0, 5)}`,
          id: item?.identifier,
          collectionAddress: item?.contract || '',
          price: 0.1,
          highestBid: 0,
          iframe_url: item?.iframe_url || '',
        } as INftCard
      })
      return { items: newItems, pageInfo: openseaData }
    },
    initialPageParam: null,
    getNextPageParam: (lastPage) => lastPage?.pageInfo?.next || lastPage?.pageInfo?.endCursor || null,
    enabled: !!contractAddress,
  })
  useEffect(() => {
    const urlUpdateParams: any = {
      ...variables,
      isSmall: isSmallCard,
      type: 'nfts',
      attrs: undefined,
    }

    if (filterParams.attrs) {
      // Serialize attrs for URL if they exist
      if (filterParams.attrs && filterParams.attrs.length > 0) {
        urlUpdateParams.attrs = serializeAttrs(filterParams.attrs)
      }
    }

    updateSearchParams(urlUpdateParams)
  }, [variables, isSmallCard, filterParams.attrs])

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])
  // useEffect(() => {
  //   if (fetchNftList && fetchNftList?.length > 0) {
  //     setTokenList(fetchNftList)
  //   }
  // }, [fetchNftList])
  // searchNft
  const onSearch = useCallback(
    async (value: string) => {
      console.log('value', filterParams)
      if (filterParams.status === 'noSale') {
        variables.price_lte = 0
      } else if (filterParams.status === 'buy') {
        variables.price_gte = 0
      } else {
        delete variables.price_lte
        delete variables.price_gte
      }
      if (value) {
        setVariables({
          ...variables,
          tokenId_contains: value,
        })
      } else {
        delete variables.tokenId_contains
        setVariables({
          ...variables,
        })
      }
    },
    [filterParams, variables]
  )
  const onDrowndown = (value: string | string[]) => {
    if (isFusion) {
      delete variables.price_not
      delete variables.priceEndTime_gt
      if (value === 'trending') {
        setVariables({
          ...variables,
          orderBy: 'level',
          orderDirection: 'desc',
        })
      } else if (value === 'recently') {
        setVariables({
          ...variables,
          orderBy: 'priceStartTime',
          orderDirection: 'desc',
          // price_not: 0,
          // priceEndTime_gt: dayjs().unix(),
        })
      } else if (value === 'low') {
        setVariables({
          ...variables,
          orderBy: 'price',
          // price_not: 0,
          // priceEndTime_gt: dayjs().unix(),
          orderDirection: 'asc',
        })
      } else if (value === 'high') {
        setVariables({
          ...variables,
          orderBy: 'price',
          orderDirection: 'desc',
          // price_not: 0,
          // priceEndTime_gt: dayjs().unix(),
        })
      }
      setSelectedDropdown(value as string)
    }
  }
  // useEffect(() => {
  //   if (tokenList.length > 0 && !isFusion) {
  //     queryClient.invalidateQueries({ queryKey: ['collectionNfts', contractAddress, variables] })
  //   }
  // }, [tokenList, isFusion, contractAddress, variables])
  useEffect(() => {
    if (Object.keys(filterParams).length === 0 && isFusion) {
      delete variables.priceEndTime_gt
      delete variables.price_gte
      delete variables.priceEndTime_lt
      delete variables.price_lte
      setVariables({
        ...variables,
      })
    }
    if (filterParams.status && isFusion) {
      if (filterParams.status === 'all') {
        delete variables.priceEndTime_gt
        delete variables.price_gte
        delete variables.priceEndTime_lt
        delete variables.price_lte
        setVariables({
          ...variables,
        })
      } else if (filterParams.status === 'buy') {
        delete variables.priceEndTime_lt
        delete variables.price_lte
        const currentTimestampInSeconds = dayjs().unix()
        setVariables({
          ...variables,
          priceEndTime_gt: currentTimestampInSeconds,
          price_gte: 0,
        })
      } else if (filterParams.status === 'noSale') {
        delete variables.priceEndTime_gt
        delete variables.price_gte
        const currentTimestampInSeconds = dayjs().unix()
        setVariables({
          ...variables,
          price_lte: 0,
          priceEndTime_lt: currentTimestampInSeconds,
        })
      }
    }
  }, [filterParams, isFusion])
  useEffect(() => {
    if (variables && isFusion) {
      queryClient.invalidateQueries({ queryKey: ['collectionNfts', contractAddress, variables] })
    }
  }, [variables, isFusion, variables])
  useEffect(() => {
    if (filterParams?.price && isFusion) {
      if (filterParams?.price?.minValue) {
        delete variables.price_gte
        delete variables.priceEndTime_gt
        const currentTimestampInSeconds = dayjs().unix()
        variables.price_gte = Number(parseEther(filterParams.price.minValue).toString())
        variables.priceEndTime_gt = currentTimestampInSeconds
      }
      if (!filterParams?.price?.minValue) {
        delete variables.price_gte
      }
      if (!filterParams?.price?.maxValue) {
        delete variables.price_lte
      }
      if (filterParams?.price?.maxValue) {
        delete variables.price_lte
        delete variables.priceEndTime_gt
        const currentTimestampInSeconds = dayjs().unix()
        variables.price_lte = Number(parseEther(filterParams.price.maxValue).toString())
        variables.priceEndTime_gt = currentTimestampInSeconds
      }
      if (!filterParams?.price?.minValue && !filterParams?.price?.maxValue) {
        delete variables.priceEndTime_gt
      }
      setVariables({
        ...variables,
      })
    }
  }, [filterParams?.price, isFusion])
  const changeAttrs = useDebounceCallback((arr) => {
    setAttrs(arr)
  }, 200)
  useEffect(() => {
    if (filterParams.attrs?.length > 0 && isFusion) {
      changeAttrs(filterParams.attrs)
    } else {
      changeAttrs([])
    }
  }, [filterParams, isFusion])
  useEffect(() => {
    if (attrs && isFusion) {
      queryClient.invalidateQueries({ queryKey: ['collectionNfts', contractAddress, variables] })
    }
  }, [attrs, isFusion, variables])
  return (
    <GraphContext.Provider
      value={{
        refetchGraph: refetch,
      }}
    >
      <FilterContext.Provider value={filterContext}>
        <div className="flex flex-col gap-4 py-6">
          <NftFilters
            onSearch={onSearch}
            onDrowndown={onDrowndown}
            refetch={refetch}
            isAlg={isFusion}
            defaultOrder={selectedDropdown}
            defaultSearch={variables.tokenId_contains || ''}
            defaultTab={isSmallCard ? 'min' : 'large'}
          />
          {isLoading && <CardListSkeleton />}
          <div className="flex w-full flex-row gap-3 pt-1">
            {isFilter && isFusion && (
              <div className="lg:sticky">
                <NftFilterPanel onClose={() => setIsFilter(false)} options={filterOptions} className="mt-0" />
              </div>
            )}
            {!isLoading && data?.pages?.[0]?.items.length === 0 && <EmptyComp className="w-full sm:h-[60vh]" />}
            {isPhone && !isSmallCard ? (
              <div
                className={twMerge('grid h-fit grid-cols-2 gap-3', data?.pages?.[0]?.items?.length === 0 && 'hidden')}
              >
                {/* {tokenList?.map((nft) => <NftCard key={nft.id} {...nft} />)} */}
                {data?.pages.map((page: { items: (React.JSX.IntrinsicAttributes & INftCard)[] }) =>
                  page.items.map((nft: React.JSX.IntrinsicAttributes & INftCard) => <NftCard key={nft.id} {...nft} />)
                )}
                {isFetchingNextPage && (
                  <>
                    {new Array(20)
                      .fill('')
                      ?.map((_, index) => <CardSkeleton key={index} isSmall={isPhone || isSmallCard} />)}
                  </>
                )}
                <div ref={ref}>{isFetchingNextPage ? <p /> : hasNextPage ? <p /> : <p />}</div>
              </div>
            ) : (
              <div className="w-full">
                <div
                  className={twMerge('grid h-fit w-full gap-3', data?.pages?.[0]?.items?.length === 0 && 'hidden')}
                  style={{
                    gridTemplateColumns: `repeat(auto-fill, minmax(${isSmallCard ? '180px' : isPad ? '180px' : '260px'}, 1fr))`,
                  }}
                >
                  {/* {tokenList?.map((nft) => <NftCard key={nft.id} {...nft} isSmall={isSmallCard} />)} */}
                  {data?.pages.map((page: { items: (React.JSX.IntrinsicAttributes & INftCard)[] }) =>
                    page.items.map((nft: React.JSX.IntrinsicAttributes & INftCard) => (
                      <NftCard key={nft.id} {...nft} isSmall={isSmallCard} />
                    ))
                  )}
                  {isFetchingNextPage && (
                    <>
                      {new Array(20)
                        .fill('')
                        ?.map((_, index) => <CardSkeleton key={index} isSmall={isPhone || isSmallCard} />)}
                    </>
                  )}
                  <div ref={ref}>{isFetchingNextPage ? <p /> : hasNextPage ? <p /> : <p />}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </FilterContext.Provider>
    </GraphContext.Provider>
  )
}

export default memo(NftList)
