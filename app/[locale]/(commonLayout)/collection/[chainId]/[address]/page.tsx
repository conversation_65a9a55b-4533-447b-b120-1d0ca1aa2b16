'use client'

import React, { Key, memo, useEffect, useMemo, useState } from 'react'

import { useTranslate } from '@tolgee/react'
import { useParams, useSearchParams } from 'next/navigation'

import BackComp from '@/components/BackComp'
import Tab from '@/components/library/Tab'

import collectionStore from '@/store/collection-store'

import Activity from '../../../../../../components/Activity'
// import useCollectionList2 from '@/components/TrendCard/hooks/useCollectionList2'
import BaseInfo from './components/BaseInfo'
import BgAndCover from './components/BgAndCover'
import NftInfo from './components/NftInfo'
import NftList from './components/NftList'
import Swap from './components/Swap'
// import useGetCollectionDetail from './hooks/useGetCollectionDetail'
import { chainInfo } from '@/constant/chainInfo'
import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
// import useGetAllCollection from '@/hooks/query/collection/useGetAllCollection'
// import useGetIndexCollectionDeatil from '@/hooks/query/collection/useGetIndexCollectionDeatil'
import useRoyalty from '@/hooks/useRoyalty'
import { supportChainId } from '@/libs/contract/wagmiConf'
// import { formatCount } from '@/libs/common/format'
import { collection_graph } from '@/libs/graph'
import { getNativeTokenSymbol, toLowerCaseLettersOnly } from '@/libs/utils/util'
import { IAddress } from '@/types/IAddress'
import { IHomeCollectionData } from '@/types/ITable'

type IType = 'nfts' | 'activity'

const CollectionInfo = () => {
  const { t } = useTranslate('collection')
  const searchParams = useSearchParams()
  const urlTab = searchParams.get('type') as IType
  const collections = collectionStore((state) => state.collections)

  const fusion = useFusionAddress()
  const params = useParams()
  const ciqiRoyalties = useRoyalty()
  // const { data: graphData } = useGetCollectionDetail(params?.address as IAddress)

  // console.log('graphData', graphData)
  const collectionItem: IHomeCollectionData = useMemo(() => {
    return (
      collections.find(
        (item) => toLowerCaseLettersOnly(item?.address) === toLowerCaseLettersOnly(params.address as IAddress)
      ) || ({} as IHomeCollectionData)
    )
  }, [collections, params])
  const collectionName = useMemo(() => {
    return collectionItem?.name
  }, [collectionItem])
  const ownerAddress = useMemo(() => {
    return collectionItem?.creatorAddress
  }, [collectionItem])
  const tokenPrice = useMemo(() => {
    return Number(collectionItem?.tokenPrice) || 0
  }, [collectionItem?.tokenPrice])
  const volumn = useMemo(() => {
    return Number(collectionItem?.volume) || 0
  }, [collectionItem?.volume])
  const floor = useMemo(() => {
    return Number(collectionItem?.floorPrice || '') || 0
  }, [collectionItem])
  const supply = useMemo(() => {
    return Number(collectionItem.supplyCount) || 0
  }, [collectionItem])
  const minted = useMemo(() => {
    return Number(collectionItem?.minted) || 0
  }, [collectionItem])
  const owners = useMemo(() => {
    return Number(collectionItem?.owners) || 0
  }, [collectionItem])
  const chain = useMemo(() => {
    return chainInfo[collectionItem.chainId]?.name || ''
  }, [collectionItem])
  const avatar = useMemo(() => {
    return collectionItem?.logo
  }, [collectionItem])
  const background = useMemo(() => {
    return collectionItem?.cover
  }, [collectionItem])
  const symbol = useMemo(() => {
    return getNativeTokenSymbol(collectionItem?.chainId) || ''
  }, [collectionItem])
  const [disableKey, setDisableKey] = useState<IType[]>([])
  const tabList = useMemo(() => {
    return [
      {
        key: 'nfts',
        title: t('collection_items'),
      },
      {
        key: 'activity',
        title: t('collection_activity'),
        isCommingSoon: disableKey.includes('activity'),
      },
    ]
  }, [t, disableKey])
  const [currentTab, setCurrentTab] = useState<IType>(urlTab || 'nfts')
  useEffect(() => {
    if (collectionItem.chainId != supportChainId) {
      setDisableKey(['activity'])
    } else {
      setDisableKey([])
    }
  }, [collectionItem])
  const handleTab = (key: Key) => {
    setCurrentTab(key as IType)
  }
  const renderTab = (tab: IType) => {
    switch (tab) {
      case 'nfts':
        return <NftList collectionItem={collectionItem} />
      case 'activity':
        return (
          <Activity
            graphQl={{ ...collection_graph }}
            type="collection"
            topVariables={{
              contractAddress: toLowerCaseLettersOnly(collectionItem.address) as IAddress,
            }}
          />
        )
      default:
        return <NftList collectionItem={collectionItem} />
    }
  }

  const royalties = useMemo(() => {
    return collectionItem.address === fusion ? ciqiRoyalties : 0
  }, [collectionItem, fusion, ciqiRoyalties])
  return (
    <div className="flex w-full flex-col">
      <BackComp className="pb-4 pt-0 sm:pb-6 sm:pt-2" />
      <div className="flex flex-col gap-12">
        <BgAndCover avatar={avatar} background={background} />
        <div className="flex flex-col-reverse lg:flex-row lg:gap-5">
          <div className="flex-1">
            <BaseInfo
              className=" hidden lg:flex"
              ownerAddress={ownerAddress}
              collectionName={collectionName}
              royalties={royalties}
            />
            <div className="border-b border-borderPrimary pt-8">
              <Tab
                disabledKeys={disableKey}
                classNames={{ tabList: 'gap-0' }}
                list={tabList}
                selectedKey={currentTab}
                handleTab={handleTab}
              />
            </div>
            {renderTab(currentTab)}
          </div>
          <div className="flex w-full flex-col gap-5  md:w-[588px] lg:w-[450px]">
            <BaseInfo
              className="flex lg:hidden"
              ownerAddress={ownerAddress}
              collectionName={collectionName}
              royalties={royalties}
            />
            <NftInfo
              isLoading={false}
              address={ownerAddress}
              price={tokenPrice}
              volume={volumn}
              floor={floor}
              supply={supply}
              minted={minted}
              owners={owners}
              chain={chain}
              symbol={symbol}
            />
            <div className=" sticky top-[88px]">
              <Swap className="mt-0" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default memo(CollectionInfo)
