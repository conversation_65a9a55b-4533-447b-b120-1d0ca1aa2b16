import { useMemo } from 'react'

import collectionStore from '@/store/collection-store'

import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
import useRoyaltyInfo from '@/hooks/contract/fusion/useRoyaltyInfo'
import useTokenOwner from '@/hooks/contract/fusion/useTokenOwner'
import useFetchGetNftDeatil from '@/hooks/query/nft/useFetchGetNftDeatil'
// import useGetNftDetail from '@/hooks/query/nft/useGetNftDetail'
import useGetUserInfo from '@/hooks/query/user/useGetUserInfo'
// import useAlgChain from '@/hooks/useAlgChain'
// import useBaseChain from '@/hooks/useBaseChain'
// import useBSCChain from '@/hooks/useBSCChain'
import useRoyalty from '@/hooks/useRoyalty'
import { formatTokenId } from '@/libs/common/format'
import { useAppKitAccount } from '@/libs/contract/wagmiConf'
import { toLowerCaseLettersOnly } from '@/libs/utils/util'
import { IAddress } from '@/types/IAddress'
import { IChain } from '@/types/IChain'

import { ITokenDetail } from '../types'

const useTokenDetail = (tokenId: string | undefined, collectionAddress: IAddress, topChainId?: IChain) => {
  const royalties = useRoyalty()
  const { address } = useAppKitAccount()
  const collections = collectionStore((state) => state.collections)
  // const algChain = useAlgChain()
  // const baseChain = useBaseChain()
  // const bscChain = useBSCChain()
  const collectionInfo = useMemo(() => {
    if (collections?.length > 0) {
      return collections.find(
        (item) => toLowerCaseLettersOnly(item.address) === toLowerCaseLettersOnly(collectionAddress)
      )
    }
    return null
  }, [collectionAddress, collections])
  const chainId = useMemo(() => (collectionInfo?.chainId || topChainId) as IChain, [collectionInfo])
  const { nftDetail, isError, isLoading, refetch, isRefetching } = useFetchGetNftDeatil(
    collectionAddress,
    tokenId!,
    chainId
  )
  // useFetchGetNftDeatil(collectionAddress, tokenId!)
  const {
    ownerAddress,
    // creatorAddress,
    isLoading: isOwnerLoading,
    refetch: ownerRefetch,
  } = useTokenOwner(tokenId!, address, collectionAddress)
  const { royaltyInfo } = useRoyaltyInfo(tokenId!, '0', collectionAddress, chainId)
  const { userInfo: ownerUserInfo } = useGetUserInfo(ownerAddress)
  const { userInfo: creatorUserInfo } = useGetUserInfo(royaltyInfo.creator)
  const fusion = useFusionAddress()
  const isOwner = useMemo(() => ownerAddress === address, [address, ownerAddress])

  const isCiqi = useMemo(() => {
    return toLowerCaseLettersOnly(collectionInfo?.address as IAddress) === toLowerCaseLettersOnly(fusion)
  }, [collectionInfo?.address, fusion])

  const royaltieList = useMemo(() => {
    return [
      {
        avatar:
          creatorUserInfo?.avatar ||
          ownerUserInfo?.avatar ||
          'https://imagedelivery.net/VyzQkEKmILrpk6KkAavk8A/avatar/Yellow.png/avatar',
        address: creatorUserInfo?.address || ownerAddress,
        royalties: 100,
      },
    ]
  }, [
    // chainId,
    // algChain,
    // baseChain,
    ownerUserInfo?.avatar,
    creatorUserInfo?.avatar,
    creatorUserInfo?.address,
    ownerAddress,
  ])

  const overview = useMemo(() => {
    return {
      description: nftDetail?.description || '',
      royalties: royalties,
      royaltieList: royaltieList,
    }
  }, [nftDetail, royalties, royaltieList])

  const properties = useMemo(() => nftDetail?.attributes || [], [nftDetail])

  const iframeUrl = useMemo(() => nftDetail?.iframe_url || '', [nftDetail])

  const baseInfo = useMemo(() => {
    return {
      collection: {
        avatar: collectionInfo?.logo || '',
        name: collectionInfo?.name || '',
      },
      name: isCiqi ? `Rabbit ${formatTokenId(tokenId || '')}` : nftDetail?.name || '',
      royalties: royalties,
    }
  }, [nftDetail, royalties, collectionInfo, isCiqi, tokenId])

  const creatorInfo = useMemo(() => {
    return {
      avatar: creatorUserInfo?.avatar || 'https://imagedelivery.net/VyzQkEKmILrpk6KkAavk8A/avatar/Yellow.png/avatar',
      address: creatorUserInfo?.address as IAddress,
      name: creatorUserInfo?.displayName || '',
    }
  }, [
    // chainId,
    // algChain,
    // baseChain,
    creatorUserInfo?.avatar,
    creatorUserInfo?.address,
    creatorUserInfo?.displayName,
    // creatorAddress,
  ])

  const ownerInfo = useMemo(() => {
    return {
      avatar: ownerUserInfo?.avatar || 'https://imagedelivery.net/VyzQkEKmILrpk6KkAavk8A/avatar/Yellow.png/avatar',
      address: (ownerUserInfo?.address || ownerAddress) as IAddress,
      name: ownerUserInfo?.displayName || '',
    }
  }, [
    // chainId,
    // algChain,
    // baseChain,
    ownerUserInfo?.avatar,
    ownerUserInfo?.address,
    ownerUserInfo?.displayName,
    ownerAddress,
  ])

  const tokenDetail: ITokenDetail = {
    overview,
    properties,
    baseInfo,
    iframeUrl,
    metadata: nftDetail,
    creatorInfo,
    ownerInfo,
    tokenId: tokenId || '',
    collectionAddress: collectionAddress,
    chainId: chainId,
    isCiqi: isCiqi,
  }

  return {
    isCiqi,
    tokenDetail,
    isOwner,
    overview,
    properties,
    baseInfo,
    iframeUrl,
    metadata: nftDetail,
    creatorInfo,
    ownerInfo,
    isError,
    isLoading,
    isRefetching,
    isOwnerLoading,
    metadataRefetch: refetch,
    ownerRefetch,
  }
}

export default useTokenDetail
