import React from 'react'

import { Metadata } from 'next'

import { toLowerCaseLettersOnly } from '@/libs/utils/util'
import { IResponse } from '@/request/type/response'
import { getTolgeeInstance } from '@/tolgee/server'
import { IPumpCreateparams } from '@/types/IPump'
// import { <PERSON><PERSON>hai<PERSON> } from '@/types/IChain'
import { IHomeCollectionData } from '@/types/ITable'

const fusionApi = process.env.NEXT_PUBLIC_MEATADATA_API
const algChain = process.env.NEXT_PUBLIC_ALG_CHAIN
const baseChain = process.env.NEXT_PUBLIC_BASE_CHAIN
const bscChain = process.env.NEXT_PUBLIC_BSC_CHAIN
const ciqiAddress = process.env.NEXT_PUBLIC_FUSION
const apiKey = process.env.NEXT_PUBLIC_OPENSEA_API_KEY
const reqApi = process.env.NEXT_PUBLIC_REQUEST_API
const fusionUrl = process.env.NEXT_PUBLIC_FUSION_HREF
interface Props {
  params: { address: string; id: string; locale: string; chainId: string }
}

const getChangeChainId = (chainId: number | string) => {
  switch (Number(chainId)) {
    case 1:
      return 'ethereum'
    case 137:
      return 'matic'
    default:
      return 'ethereum'
  }
}
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { address, id, locale, chainId } = params
  const tolgee = await getTolgeeInstance(locale)
  console.log('params', params)
  const response = await fetch(
    `${reqApi}/home/<USER>
  )
  const pumpCollection = await fetch(`${reqApi}/collection/find?chainId=${chainId}&&contractAddress=${address}`)
  const collData = (await response.json()) as IResponse<IHomeCollectionData>
  const pumpData = (await pumpCollection.json()) as IResponse<IPumpCreateparams>
  const collectionItem = collData?.data
  const pumpItem = pumpData?.data
  console.log('pumpItem', pumpItem)
  console.log('collectionItem', collData)
  let data: any = {}
  try {
    if (Number(collectionItem?.chainId) == Number(algChain)) {
      const params = new URLSearchParams()
      params.append('tokenIds', id)
      params.append('contractAddress', address)
      const response = await fetch(`${fusionApi}token/metadata?${params.toString()}`)
      const responseData = await response.json()
      if (responseData?.code == 200) {
        const meataData = responseData?.data?.[0]
        data = {
          ...meataData,
        }
      }
      if (toLowerCaseLettersOnly(collectionItem.address) == toLowerCaseLettersOnly(ciqiAddress as string)) {
        data.name = `Rabbit #${data?.tokenId}`
      }
    } else if (
      Number(pumpItem?.chainId) == Number(algChain) ||
      Number(pumpItem?.chainId) == Number(baseChain) ||
      Number(pumpItem?.chainId) == Number(bscChain)
    ) {
      const params = new URLSearchParams()
      params.append('tokenIds', id)
      params.append('contractAddress', address)
      const response = await fetch(`${fusionApi}token/metadata?${params.toString()}`)
      const responseData = await response.json()
      if (responseData?.code == 200) {
        const meataData = responseData?.data?.[0]
        data = {
          ...meataData,
        }
      }
      if (toLowerCaseLettersOnly(collectionItem.address) == toLowerCaseLettersOnly(ciqiAddress as string)) {
        data.name = `Rabbit #${data?.tokenId}`
      }
    } else {
      const apiUrl = `https://api.opensea.io/api/v2/chain/${getChangeChainId(collectionItem.chainId)}/contract/${address}/nfts/${id}`
      console.log('apiurl', apiUrl)
      const response = await fetch(apiUrl, {
        headers: {
          'X-API-KEY': `${apiKey}`,
          'Content-Type': 'application/json',
        },
      })
      const responseData = await response.json()
      const nft = responseData?.nft
      data = {
        ...nft,
      }
    }
  } catch (error) {
    console.error('getMetaData-Error:', error)
  }
  return {
    title: tolgee.t('nft_head_title', { nftname: `${data?.name} - ${collectionItem?.name || pumpItem?.name}` }),
    authors: [
      {
        name: `Created by ${collectionItem?.name || pumpItem?.name}`,
      },
    ],
    description: `${data?.description || tolgee.t('home_head_desc')}`,
    twitter: {
      title: `${data?.name} - ${collectionItem?.name || pumpItem?.name}| Fusion`,
      images: [
        {
          alt: `${data?.name}`,
          url: `${data?.image || data?.image_url}`,
        },
      ],
      creator: `Created by ${collectionItem?.name || pumpItem?.name}`,
    },
    openGraph: {
      title: `${data?.name} - ${collectionItem?.name || pumpItem?.name}| Fusion`,
      images: [
        {
          alt: `${data?.name}`,
          url: `${data?.image || data?.image_url}`,
        },
      ],
      creators: `Created by ${collectionItem?.name || pumpItem?.name}`,
      siteName: tolgee.t('nft_head_title', { nftname: `${data?.name} - ${collectionItem?.name || pumpItem?.name}` }),
    },
    metadataBase: new URL(fusionUrl as string),
  }
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return <>{children}</>
}
