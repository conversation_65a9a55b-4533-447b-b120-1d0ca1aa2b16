import { gql } from '@apollo/client'

export const UserNftQueryDocument = gql`
  query UserNftQuery(
    $after: String
    $limit: Int = 20
    $orderBy: String
    $orderDirection: String
    $contractAddress: String
    $chainId: Int
    $price_lte: BigInt
    $price_gte: BigInt
    $priceEndTime_gt: BigInt
    $tokenId_contains: String
    $priceEndTime_lt: BigInt
    $ownerId: String
    $price_not: BigInt
    $level: Int
    $contractAddress1: String
  ) {
    nfts(
      after: $after
      limit: $limit
      orderBy: $orderBy
      orderDirection: $orderDirection
      where: {
        OR: [
          {
            contractAddress: $contractAddress
            isBurn: false
            chainId: $chainId
            price_lte: $price_lte
            price_gte: $price_gte
            priceEndTime_gt: $priceEndTime_gt
            tokenId_contains: $tokenId_contains
            priceEndTime_lt: $priceEndTime_lt
            ownerId: $ownerId
            price_not: $price_not
            level: $level
          }
          {
            contractAddress: $contractAddress1
            isBurn: false
            chainId: $chainId
            price_lte: $price_lte
            price_gte: $price_gte
            priceEndTime_gt: $priceEndTime_gt
            tokenId_contains: $tokenId_contains
            priceEndTime_lt: $priceEndTime_lt
            ownerId: $ownerId
            price_not: $price_not
          }
        ]
      }
    ) {
      items {
        createTime
        isBurn
        level
        priceEndTime
        priceStartTime
        tokenId
        tokenURI
        contractAddress
        isLocked
        offers(where: { isCancel: false, price_gt: "0" }) {
          items {
            id
            price
            startTime
            endTime
          }
        }
        price
        ownerId
        chainId
      }
      pageInfo {
        endCursor
        hasNextPage
        hasPreviousPage
        startCursor
      }
    }
  }
`
export const SearchOwneLaunch = gql`
  query MyQuery($creator: String = "", $chainId: Int = 10) {
    pumpLaunchedERC404s(limit: 10, where: { chainId: $chainId, creator: $creator }) {
      items {
        actuallyTotalSupply
        assetPool
        chainId
        createTime
        creator
        currPrice
        erc404Token
      }
      totalCount
    }
  }
`

export const SearchOwnerNfts = gql`
  query UserNftQuery(
    $after: String
    $limit: Int = 20
    $orderBy: String
    $orderDirection: String
    $chainId: Int
    $price_lte: BigInt
    $price_gte: BigInt
    $priceEndTime_gt: BigInt
    $tokenId_contains: String
    $priceEndTime_lt: BigInt
    $ownerId: String
    $price_not: BigInt
    $contractAddress_in: [String] = ""
  ) {
    nfts(
      after: $after
      limit: $limit
      orderBy: $orderBy
      orderDirection: $orderDirection
      where: {
        isBurn: false
        chainId: $chainId
        price_lte: $price_lte
        price_gte: $price_gte
        priceEndTime_gt: $priceEndTime_gt
        tokenId_contains: $tokenId_contains
        priceEndTime_lt: $priceEndTime_lt
        ownerId: $ownerId
        price_not: $price_not
        contractAddress_in: $contractAddress_in
      }
    ) {
      items {
        createTime
        isBurn
        level
        priceEndTime
        priceStartTime
        tokenId
        tokenURI
        contractAddress
        isLocked
        offers(where: { isCancel: false, price_gt: "0" }) {
          items {
            id
            price
            startTime
            endTime
          }
        }
        price
        ownerId
        chainId
      }
      pageInfo {
        endCursor
        hasNextPage
        hasPreviousPage
        startCursor
      }
      totalCount
    }
  }
`
export const SearchOwnerNftsStr = `
  query UserNftQuery(
    $after: String
    $limit: Int = 20
    $orderBy: String
    $orderDirection: String
    $chainId: Int
    $price_lte: BigInt
    $price_gte: BigInt
    $priceEndTime_gt: BigInt
    $tokenId_contains: String
    $priceEndTime_lt: BigInt
    $ownerId: String
    $price_not: BigInt
    $contractAddress_in: [String] = ""
  ) {
    nfts(
      after: $after
      limit: $limit
      orderBy: $orderBy
      orderDirection: $orderDirection
      where: {
        isBurn: false
        chainId: $chainId
        price_lte: $price_lte
        price_gte: $price_gte
        priceEndTime_gt: $priceEndTime_gt
        tokenId_contains: $tokenId_contains
        priceEndTime_lt: $priceEndTime_lt
        ownerId: $ownerId
        price_not: $price_not
        contractAddress_in: $contractAddress_in
      }
    ) {
      items {
        createTime
        isBurn
        level
        priceEndTime
        priceStartTime
        tokenId
        tokenURI
        contractAddress
        isLocked
        offers(where: {isCancel: false, price_gt: "0"}) {
          items {
            id
            price
            startTime
            endTime
          }
        }
        price
        ownerId
        chainId
      }
      pageInfo {
        endCursor
        hasNextPage
        hasPreviousPage
        startCursor
      }
      totalCount
    }
  }
`

export const SearchOwnerPumpTransactions = gql`
  query MyQuery(
    $eventType_in: [dbPumpEventType!] = SOLD_TOKEN
    $fromId: String = ""
    $tokenAddress_in: [String!] = ""
  ) {
    pumpEvents(where: { eventType_in: $eventType_in, fromId: $fromId, tokenAddress_in: $tokenAddress_in }) {
      totalCount
      items {
        amount
        chainId
        contractAddress
        cost
        createTime
        eventType
        fromId
        id
        tokenAddress
        transactionHash
        updateTime
      }
    }
  }
`

export const SearchOwnerPumpAccounts = gql`
  query MyQuery($ownerId: String = "") {
    pumpAccounts(where: { ownerId: $ownerId }) {
      items {
        amount
        chainId
        createTime
        erc404TokenAddr
        id
        investment
        ownerId
        revenue
        salesAmount
        updateTime
      }
    }
  }
`
