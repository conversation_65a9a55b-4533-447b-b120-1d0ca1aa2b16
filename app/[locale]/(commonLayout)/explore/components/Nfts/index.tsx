import React, { Key, memo, useEffect, useMemo, useRef, useState } from 'react'
import { LuChevronLeft, LuList<PERSON>ilter, LuRefreshCw } from 'react-icons/lu'
import { TbFilter } from 'react-icons/tb'
import { TbD<PERSON>padFilled, TbLayoutGridFilled } from 'react-icons/tb'
import { useInView } from 'react-intersection-observer'

// import { useApolloClient } from '@apollo/client'
import { Switch } from '@nextui-org/react'
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query'
import { useTranslate } from '@tolgee/react'
import dayjs from 'dayjs'
import { twMerge } from 'tailwind-merge'
import { useDebounceCallback, useMediaQuery } from 'usehooks-ts'
import { formatEther, parseEther } from 'viem'

import EmptyComp from '@/components/EmptyComp'
import ErrorAndTryComp from '@/components/ErrorAndTryComp'
import { FilterContext, IFilterContext } from '@/components/Filters/FilterContext'
import FilterPanel from '@/components/Filters/FilterPanel'
import SearchInput from '@/components/Input/SearchInput'
import Button from '@/components/library/Button'
import Dropdown, { IDropdownItemProps } from '@/components/library/Dropdown'
import CardListSkeleton from '@/components/library/Skeleton/CardListSkeleton'
import CardSkeleton from '@/components/library/Skeleton/CardSkeleton'
import Tab from '@/components/library/Tab'
import NftCard, { INftCard } from '@/components/NftCard'

import collectionStore from '@/store/collection-store'

import { SearchNftQueryQueryVariables } from '@/graphql/generated'
import { GraphContext } from '@/graphql/GraphContext'
import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
import { isEVMChain } from '@/hooks/useIsEvm'
import { useReadNftSearchParams, useUpdateNftSearchParams } from '@/hooks/useUpdateSearchParams'
import { getChainInfo } from '@/libs/common/chain'
import { formatTokenId } from '@/libs/common/format'
import { useAppKitAccount } from '@/libs/contract/wagmiConf'
import { ponder_collectionNft } from '@/libs/graph'
import { toLowerCaseLettersOnly } from '@/libs/utils/util'
import { tokenSearch } from '@/request/api/nft'

const iconTabs = [
  {
    key: 'lager',
    title: <TbLayoutGridFilled />,
  },
  {
    key: 'min',
    title: <TbDialpadFilled />,
  },
]
const Nfts = () => {
  // const { isLoading, isError, tokenList, isSuccess, refetch } = useNfts()
  // const client = useApolloClient()
  const updateSearchParams = useUpdateNftSearchParams('nfts')
  const readSearchParams = useReadNftSearchParams('nfts')
  const { getChainCurrency } = getChainInfo()
  const urlParams = readSearchParams()
  const queryClient = useQueryClient()
  const collections = collectionStore((state) => state.collections)
  const { ref, inView } = useInView({
    threshold: 1, // Trigger when fully visible
  })
  const { address } = useAppKitAccount()
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const isPhone = useMediaQuery('(max-width: 768px)')
  const isPad = useMediaQuery('(max-width: 1024px)')
  // const tokenList = useMemo(() => [...graphTokenList], [graphTokenList])
  // const [realNftList, setRealNftList] = useState<INftItem[]>([])
  // const [changeLoading, setChangeLoading] = useState<boolean>(false)
  const [isSmall, setIsSmall] = useState<boolean>(urlParams.isSmall || false)
  const [isFitlter, setIsFilter] = useState<boolean>(urlParams.isFilter || false)
  const [selectedDropdown, setSelectedDropdown] = useState<string>(urlParams.selectedDropdown || 'trending')
  const [filterParams, setFilterParams] = useState<any>(() => {
    const initialFilterParams: any = { status: 'all', chainId: 'all' }
    if (urlParams.chainId) {
      initialFilterParams.chainId = urlParams.chainId.toString()
    }
    if (urlParams.price_gte && urlParams.priceEndTime_gt) {
      initialFilterParams.status = 'buy'
    }
    if (urlParams.price_lte === '0' && urlParams.priceEndTime_lt) {
      initialFilterParams.status = 'noSale'
    }
    if (urlParams.price_lte || urlParams.price_gte) {
      initialFilterParams.price = {
        minValue: urlParams.price_gte ? formatEther(BigInt(urlParams.price_gte)) : '',
        maxValue:
          urlParams.price_lte && initialFilterParams.status !== 'noSale'
            ? formatEther(BigInt(urlParams.price_lte))
            : '',
        symbol: getChainCurrency(urlParams.chainId || 8921) || 'ALG',
      }
    }

    return initialFilterParams
  })
  const [filterState, setFilterState] = useState({
    status: null,
    price: {
      minValue: null,
      maxValue: null,
    },
  })
  const { t } = useTranslate(['explore', 'filters', 'common'])
  const fusion = useFusionAddress()
  const [variables, setVariables] = useState<SearchNftQueryQueryVariables>(() => {
    return {
      orderBy: urlParams.orderBy || 'level',
      orderDirection: urlParams.orderDirection || 'desc',
      ...urlParams,
      // contractAddress: toLowerCaseLettersOnly(fusion),}
    }
  })
  const [attrs, setAttrs] = useState<any[]>([])

  // useEffect(() => {
  //   const urlParams = readSearchParams()
  //   if (Object.keys(urlParams).length > 0) {
  //     const newVariables = { ...variables }

  //     if (urlParams.tokenId_contains !== undefined) {
  //       newVariables.tokenId_contains = urlParams.tokenId_contains
  //     }

  //     if (urlParams.contractAddress !== undefined) {
  //       newVariables.contractAddress = urlParams.contractAddress
  //     }

  //     if (urlParams.chainId !== undefined) {
  //       newVariables.chainId = urlParams.chainId
  //     }

  //     if (urlParams.price_gte !== undefined) {
  //       newVariables.price_gte = urlParams.price_gte
  //     }

  //     if (urlParams.price_lte !== undefined) {
  //       newVariables.price_lte = urlParams.price_lte
  //     }

  //     if (urlParams.priceEndTime_gt !== undefined) {
  //       newVariables.priceEndTime_gt = urlParams.priceEndTime_gt
  //     }

  //     if (urlParams.priceEndTime_lt !== undefined) {
  //       newVariables.priceEndTime_lt = urlParams.priceEndTime_lt
  //     }

  //     if (urlParams.price_not !== undefined) {
  //       newVariables.price_not = urlParams.price_not
  //     }

  //     if (urlParams.orderBy !== undefined) {
  //       newVariables.orderBy = urlParams.orderBy
  //     }

  //     if (urlParams.orderDirection !== undefined) {
  //       newVariables.orderDirection = urlParams.orderDirection
  //     }

  //     if (urlParams.isSmall !== undefined) {
  //       setIsSmall(urlParams.isSmall)
  //     }

  //     if (urlParams.selectedDropdown !== undefined) {
  //       setSelectedDropdown(urlParams.selectedDropdown)
  //     }
  //     setVariables(newVariables)
  //   }
  // }, [])

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, isError, refetch } = useInfiniteQuery({
    queryKey: ['exploreNfts', address, variables, isSmall],
    queryFn: async ({ pageParam = null }) => {
      const params: any = {
        query: ponder_collectionNft,
        variables: {
          orderBy: 'level',
          orderDirection: 'desc',
          ...variables,
          after: pageParam || null,
          limit: 20,
          contractAddressNotIn: ['0x00f983e3cf6882d27b186a93a85e32998f2101b4'],
          platformNot: 'LIKN',
        },
        operationName: 'SearchNftQuery',
        attrs: attrs,
        contractAddress: toLowerCaseLettersOnly(variables?.contractAddress || '') || null,
      }
      console.log('variables:', variables)

      updateSearchParams({ ...variables, isSmall: isSmall, type: 'nfts', isFilter: isFitlter })
      if (!params.attrs || params.attrs.length === 0) {
        delete params.attrs
      }
      if (params.attrs && params.attrs.length > 0) {
        params.contractAddress = toLowerCaseLettersOnly(filterParams.collection?.contractAddress)
      }
      const { data } = await tokenSearch({ ...params })
      const { tokens, pageInfo } = data
      const collectionItem = collections.find(
        (item) => toLowerCaseLettersOnly(item.address) == toLowerCaseLettersOnly(variables?.contractAddress as string)
      )
      const newItems = tokens.map((item: any) => {
        const { attributes = [] } = item
        let level = item?.level || ''
        attributes?.map((item: { trait_type: string; value: any }) => {
          if (item?.trait_type == 'Level') {
            level = item?.value
          }
        })
        return {
          cover: item?.image || '',
          collectionName: collectionItem?.name,
          name:
            toLowerCaseLettersOnly(item?.contractAddress) == toLowerCaseLettersOnly(fusion)
              ? `Rabbit #${formatTokenId(item.tokenId)}`
              : // : `${item?.name} #${formatTokenId(item.tokenId)}`,
                `${item?.name}`,
          // name: `Rabbit #${item.tokenId.toString().slice(0, 5)}`,
          id: item.tokenId,
          collectionAddress: item?.contractAddress || '',
          price: 0.1,
          highestBid: 0,
          url: item.tokenURI,
          iframe_url: item?.iframe_url || '',
          nftDeatil: {
            ownerAddress: item?.ownerId,
            price: item?.price,
            priceEndTime: item?.priceEndTime,
            priceStartTime: item?.priceStartTime,
            offers: item?.offers?.items || [],
            chainId: item?.chainId,
          },
          level: level ? (level?.toString()?.startsWith('Lv') ? level : `Lv-${level}`) : '',
        } as INftCard
      })
      console.log('newitems', newItems)
      return { items: newItems, pageInfo }
    },
    initialPageParam: null,
    getNextPageParam: (lastPage) => lastPage.pageInfo.endCursor || null,
    // enabled: !!variables,
  })
  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])
  const filterDropdownList: IDropdownItemProps[] = useMemo(() => {
    return [
      {
        key: 'trending',
        value: t('trending', { ns: 'common' }),
      },
      {
        key: 'recently',
        value: t('recently', { ns: 'common' }),
      },
      {
        key: 'low',
        value: t('low', { ns: 'common' }),
      },
      {
        key: 'high',
        value: t('high', { ns: 'common' }),
      },
    ]
  }, [t])
  const filterContext: IFilterContext = {
    smallNftCard: isSmall,
    refreshNftSize: setIsSmall,
    isFilter: isFitlter,
    changeFilter: setIsFilter,
    filterParams: filterParams,
    changeFilterParams: setFilterParams,
  }
  const filterLength = useMemo(() => {
    return (
      Object.keys(filterParams).filter((key) => {
        if (key === 'attrs' && filterParams[key]?.length === 0) {
          return false
        }
        return filterParams[key] !== null && filterParams[key] !== undefined && filterParams[key] !== 'all'
      }).length || 0
    )
  }, [filterParams])
  console.log('filterParams', filterParams)
  const onSearchNft = (value: any) => {
    if (value) {
      setVariables({
        ...variables,
        tokenId_contains: value,
      })
    } else {
      queryClient.resetQueries({
        queryKey: ['exploreNfts'],
      })
      delete variables.tokenId_contains
      setVariables({
        ...variables,
      })
    }
  }
  const onDropDown = (value: string) => {
    // delete variables.price_not
    // delete variables.priceEndTime_gt
    if (value === 'trending') {
      setVariables({
        ...variables,
        orderBy: 'level',
        orderDirection: 'desc',
      })
    } else if (value === 'recently') {
      setVariables({
        ...variables,
        orderBy: 'priceStartTime',
        orderDirection: 'desc',
        // price_not: 0,
        // priceEndTime_gt: dayjs().unix(),
      })
    } else if (value === 'low') {
      setVariables({
        ...variables,
        orderBy: 'price',
        // price_not: 0,
        // priceEndTime_gt: dayjs().unix(),
        orderDirection: 'asc',
      })
    } else if (value === 'high') {
      setVariables({
        ...variables,
        orderBy: 'price',
        orderDirection: 'desc',
        // price_not: 0,
        // priceEndTime_gt: dayjs().unix(),
      })
    }
    setSelectedDropdown(value)
  }
  const onSwitchValue = (value: boolean) => {
    if (value) {
      timerRef.current = setInterval(() => {
        refetch()
      }, 30000)
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }
  useEffect(() => {
    if (variables) {
      queryClient.invalidateQueries({ queryKey: ['exploreNfts', address, variables] })
    }
  }, [address, queryClient, variables])
  useEffect(() => {
    if (filterParams.chainId) {
      queryClient.resetQueries({
        queryKey: ['exploreNfts'],
      })
      if (filterParams.chainId === 'all') {
        delete variables.chainId
        setVariables({
          ...variables,
        })
      } else {
        setVariables({
          ...variables,
          chainId: isEVMChain(filterParams.chainId) ? Number(filterParams.chainId) : filterParams.chainId.toString(),
        })
      }
    }
  }, [filterParams.chainId])
  useEffect(() => {
    if (filterParams.status) {
      setFilterState((prev) => ({
        ...prev,
        status: filterParams.status,
      }))

      applyFilters({
        ...filterState,
        status: filterParams.status,
      })
    }
  }, [filterParams.status])
  useEffect(() => {
    if (filterParams?.price) {
      setFilterState((prev) => ({
        ...prev,
        price: {
          minValue: filterParams.price?.minValue || null,
          maxValue: filterParams.price?.maxValue || null,
        },
      }))

      applyFilters({
        ...filterState,
        price: {
          minValue: filterParams.price?.minValue || null,
          maxValue: filterParams.price?.maxValue || null,
        },
      })
    }
  }, [filterParams?.price])
  const applyFilters = (filters: any) => {
    const newVariables = { ...variables }
    const currentTimestampInSeconds = dayjs().unix()

    delete newVariables.priceEndTime_gt
    delete newVariables.priceEndTime_lt
    delete newVariables.price_gte
    delete newVariables.price_lte

    if (filters.status) {
      if (filters.status === 'all') {
      } else if (filters.status === 'buy') {
        newVariables.priceEndTime_gt = currentTimestampInSeconds
        newVariables.price_gte = 0
      } else if (filters.status === 'noSale') {
        newVariables.price_lte = 0
        newVariables.priceEndTime_lt = currentTimestampInSeconds
      }
    }

    if (filters.price) {
      if (filters.price.minValue) {
        newVariables.price_gte = Number(parseEther(filters.price.minValue).toString())
        newVariables.priceEndTime_gt = currentTimestampInSeconds
      }

      if (filters.price.maxValue) {
        newVariables.price_lte = Number(parseEther(filters.price.maxValue).toString())
        if (!newVariables.priceEndTime_gt) {
          newVariables.priceEndTime_gt = currentTimestampInSeconds
        }
      }
    }

    setVariables(newVariables)
  }
  const changeAttrs = useDebounceCallback((arr) => {
    setAttrs(arr)
  }, 200)
  useEffect(() => {
    if (filterParams.collection && filterParams.attrs?.length > 0) {
      changeAttrs(filterParams.attrs)
    } else {
      changeAttrs([])
    }
  }, [filterParams])
  useEffect(() => {
    if (filterParams?.collection && filterParams?.collection?.address) {
      setVariables({
        ...variables,
        contractAddress: toLowerCaseLettersOnly(filterParams?.collection?.address),
      })
    } else if (Object.keys(filterParams).length > 0 && !filterParams?.collection) {
      //避免副作用影响clean
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
      const { contractAddress, ...restVariables } = variables
      setVariables(restVariables)
    }
  }, [filterParams?.collection, fusion])
  useEffect(() => {
    if (attrs.length > 0) {
      queryClient.invalidateQueries({ queryKey: ['exploreNfts', address, variables] })
    }
  }, [attrs])
  const cleanParams = () => {
    const cleanedVariables = {
      orderBy: 'level',
      orderDirection: 'desc',
      // contractAddress: toLowerCaseLettersOnly(fusion),
      tokenId_contains: variables.tokenId_contains,
    }
    setFilterParams({})
    setFilterState({
      status: null,
      price: {
        minValue: null,
        maxValue: null,
      },
    })
    setVariables(cleanedVariables)
    setAttrs([])
    refetch?.()
  }
  return (
    <GraphContext.Provider
      value={{
        refetchGraph: refetch,
      }}
    >
      <FilterContext.Provider value={filterContext}>
        <div className="flex flex-col">
          <div className="flex items-center gap-4">
            {!isPhone && (
              <Button
                color="gray"
                className=" hidden gap-1.5 lg:flex"
                badgeClassName="hidden lg:flex"
                onClick={() => setIsFilter(!isFitlter)}
                badge={filterLength}
              >
                {isFitlter ? <LuChevronLeft size={20} /> : <TbFilter size={20} />}
                <span className=" text-sm font-semibold">{t('Filter', { ns: 'filters' })}</span>
              </Button>
            )}

            {filterLength > 0 && (
              <Button color="gray" className=" hidden gap-1.5 lg:flex" onClick={cleanParams}>
                <span className=" text-sm font-semibold">{t('clear_all', { ns: 'filters' })}</span>
              </Button>
            )}
            <div className="hidden items-center gap-1 lg:flex">
              <Switch onValueChange={onSwitchValue} />
              <div className="flex flex-shrink-0 items-center gap-3">
                <span className=" text-xs text-quaternary">{t('Live_data', { ns: 'explore' })}</span>
                <span className="relative flex h-3 w-3 items-center justify-center">
                  <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-success opacity-75"></span>
                  <span className="relative inline-flex h-2 w-2 rounded-full bg-success"></span>
                </span>
              </div>
            </div>
            <SearchInput
              placeholder={t('search_nfts', { ns: 'explore' })}
              onSearch={onSearchNft}
              debounceTime={500}
              defaultValue={variables.tokenId_contains || ''}
              // setLoading={setChangeLoading}
            />
            <div className="flex items-center gap-1.5">
              <Button color="gray" className="min-w-10 !p-0 lg:hidden" onClick={() => setIsFilter(!isFitlter)}>
                {/* <TbFilter size={20} className=" text-primary" /> */}
                {isFitlter ? (
                  <LuChevronLeft size={20} className=" text-primary" />
                ) : (
                  <TbFilter size={20} className=" text-primary" />
                )}
              </Button>
              <Button color="gray" className="min-w-10 !p-0 lg:hidden" onClick={() => refetch()}>
                <LuRefreshCw size={20} className=" text-primary" />
              </Button>
              <Button color="gray" className="min-w-10 !p-0 md:hidden">
                <LuListFilter size={20} className=" text-primary" />
              </Button>
              <div className="hidden md:block">
                <Dropdown list={filterDropdownList} defaultValue={selectedDropdown} onAction={onDropDown} />
              </div>
              <div className="hidden lg:block">
                <Tab
                  variants="solid"
                  classNames={{ tabList: '!px-1 !py-1', tab: 'first:!pl-3 last:!pr-3' }}
                  list={iconTabs}
                  selectedKey={isSmall ? 'min' : 'lager'}
                  handleTab={(key: Key) => {
                    setIsSmall(key === 'min')
                  }}
                />
              </div>
            </div>
          </div>
          <div className="flex flex-row items-start gap-4 p-0">
            {isFitlter && (
              <div className="top-20 lg:sticky">
                <FilterPanel onClose={() => setIsFilter(false)} cleanParams={cleanParams} />
              </div>
            )}
            {/* nfts */}
            <div className="w-full py-5">
              {/* {(!isLoading) && (
              <NftList
                isError={isError}
                isLoading={isLoading || changeLoading}
                tokenList={realNftList}
                isSmall={isSmall}
              />
            )} */}
              {isLoading && <CardListSkeleton />}
              {data?.pages?.[0]?.items?.length > 0 && (
                <>
                  {isPhone ? (
                    <div className={twMerge('grid grid-cols-2 gap-3')}>
                      {data?.pages.map((page) =>
                        page.items.map((nft: React.JSX.IntrinsicAttributes & INftCard) => (
                          <NftCard key={`${nft?.id}_${nft?.collectionAddress}`} {...nft} isSmall />
                        ))
                      )}
                      {/* {tokenList?.map((nft) => )} */}
                      {isFetchingNextPage && (
                        <>{new Array(20).fill('')?.map((_, index) => <CardSkeleton key={index} isSmall />)}</>
                      )}
                      <div ref={ref}>{isFetchingNextPage ? <p /> : hasNextPage ? <p /> : <p />}</div>
                    </div>
                  ) : (
                    <div
                      className={twMerge('grid w-full gap-3', data?.pages?.[0]?.items?.length === 0 && 'hidden')}
                      style={{
                        gridTemplateColumns: `repeat(auto-fill, minmax(${isSmall ? '170px' : isPad ? '180px' : '260px'}, 1fr))`,
                      }}
                    >
                      {data?.pages.map((page) =>
                        page.items.map((nft: React.JSX.IntrinsicAttributes & INftCard) => (
                          <NftCard key={`${nft?.id}_${nft?.collectionAddress}`} {...nft} isSmall={isSmall} />
                        ))
                      )}
                      {/* {tokenList?.map((nft) => <NftCard key={nft.id} {...nft} isSmall={isSmall} />)} */}
                      {isFetchingNextPage && (
                        <>{new Array(20).fill('')?.map((_, index) => <CardSkeleton key={index} isSmall={isSmall} />)}</>
                      )}
                      <div ref={ref}>{isFetchingNextPage ? <p /> : hasNextPage ? <p /> : <p />}</div>
                    </div>
                  )}
                </>
              )}
              {!isLoading &&
                isError &&
                (data?.pages?.[0]?.items === null ||
                  data?.pages?.[0]?.items === undefined ||
                  data?.pages?.[0]?.items.length === 0) && (
                  <ErrorAndTryComp onAction={refetch} className="sm:h-[60vh]" />
                )}
              {!isLoading &&
                !isError &&
                (data?.pages?.[0]?.items === null ||
                  data?.pages?.[0]?.items === undefined ||
                  data?.pages?.[0]?.items.length === 0) && <EmptyComp className="sm:h-[60vh]" />}
            </div>
          </div>
        </div>
      </FilterContext.Provider>
    </GraphContext.Provider>
  )
}

export default memo(Nfts)
