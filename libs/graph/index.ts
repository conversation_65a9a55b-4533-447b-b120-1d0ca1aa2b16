import { gql } from '@apollo/client'

export const collectionActivityQuery_all: string = `query MyQuery($first: Int = 10, $orderBy: Event_orderBy = id, $skip: Int = 10) {
    events(first: $first, orderBy: $orderBy, skip: $skip, orderDirection: desc) {
      blockTimestamp
      tokenId
      from
      to
      eventType
      price
      startTime
      endTime
      token {
        tokenID
        tokenURI
        owner
        price
        isBurn
        offers {
          id
          price
          startTime
          from
          endTime
        }
      }
    }
  }`

export const collectionActivity_eventType = `query MyQuery($first: Int = 10, $orderBy: Event_orderBy = id, $skip: Int = 10, $eventType: EventType) {
    events(
      first: $first
      orderBy: $orderBy
      skip: $skip
      where: {eventType: $eventType}
      orderDirection: desc
    ) {
      blockTimestamp
      tokenId
      from
      to
      price
      startTime
      endTime
      token {
        tokenID
        tokenURI
        owner
        price
        isBurn
        offers {
          id
          price
          startTime
          from
          endTime
        }
      }
      eventType
    }
  }`
export const userActivity_all = `query MyQuery($first: Int = 10, $skip: Int = 10, $from: Bytes = "", $to: Bytes = "", $orderBy: Event_orderBy = blockTimestamp) {
  eFrom:events(
    first: $first
    skip: $skip
    orderBy: $orderBy
    where: {from: $from}
    orderDirection: desc
  ) {
    blockTimestamp
    tokenId
    from
    to
    price
    startTime
    endTime
    eventType
    token {
      tokenID
      tokenURI
      owner
      price
      isBurn
      offers {
        id
        price
        startTime
        from
        endTime
      }
    }
  }
  eTo:events(
    first: $first
    skip: $skip
    orderBy: $orderBy
    where: {to: $to}
    orderDirection: desc
  ) {
    blockTimestamp
    tokenId
    from
    to
    price
    startTime
    endTime
    eventType
    token {
      tokenID
      tokenURI
      owner
      price
      isBurn
      offers {
        id
        price
        startTime
        from
        endTime
      }
    }
  }
}`

export const userActivity_eventType = `query MyQuery($first: Int = 10, $skip: Int = 10, $from: Bytes = "", $to: Bytes = "", $orderBy: Event_orderBy = blockTimestamp, $eventType: EventType = TRANSFER) {
  eFrom:events(
    first: $first
    skip: $skip
    orderBy: $orderBy
    where: {from: $from, eventType: $eventType}
    orderDirection: desc
  ) {
    blockTimestamp
    tokenId
    from
    to
    price
    startTime
    endTime
    eventType
    token {
      tokenID
      tokenURI
      owner
      price
      isBurn
      offers {
        id
        price
        startTime
        from
        endTime
      }
    }
  }
  eTo:events(
    first: $first
    skip: $skip
    orderBy: $orderBy
    where: {to: $to, eventType: $eventType}
    orderDirection: desc
  ) {
    blockTimestamp
    tokenId
    from
    to
    price
    startTime
    endTime
    eventType
    token {
      tokenID
      tokenURI
      owner
      price
      isBurn
      offers {
        id
        price
        startTime
        from
        endTime
      }
    }
  }
}`

export const nftActivity_all = `query MyQuery($tokenId: BigInt = "") {
  events(orderBy: blockTimestamp,orderDirection: desc, where: {tokenId: $tokenId}) {
    blockTimestamp
    endTime
    eventType
    from
    to
    tokenId
    startTime
    price
    token {
      isBurn
      owner
      price
      tokenID
      tokenURI
      offers {
        endTime
        from
        id
        price
        startTime
      }
    }
  }
}`

export const nftActivity_eventType = `query MyQuery($tokenId: BigInt = "") {
  events(orderBy: blockTimestamp, orderDirection: desc,where: {tokenId: $tokenId}) {
    blockTimestamp
    endTime
    eventType
    from
    to
    tokenId
    startTime
    price
    token {
      isBurn
      owner
      price
      tokenID
      tokenURI
      offers {
        endTime
        from
        id
        price
        startTime
      }
    }
  }
}`

export const nftText_search = `query MyQuery($text: String, $first: Int = 10, $skip: Int) {
  tokenSearch(text: $text, first: $first, skip: $skip, where: {isBurn: false}) {
    id
  }
}`

export const collection_detail = `query MyQuery ($priceEndTime_gt: BigInt = null){
  tokenVolumeStats(interval: hour, first: 1) {
    timestamp
    id
    totalVolume
    totalVolumeCumulative
  }
  tokens(
    where: {isBurn: false, priceEndTime_gt: $priceEndTime_gt, price_gt: 0}
    orderBy: price
    orderDirection: desc
  ) {
    price
    isBurn
    tokenID
    priceEndTime
  }
  eventCounters {
    count
    id
  }
}`

export const collection_graph = {
  all: collectionActivityQuery_all,
  eventType: collectionActivity_eventType,
  getDetail: gql`
    ${collection_detail}
  `,
}

export const user_graph = {
  all: userActivity_all,
  eventType: userActivity_eventType,
}

export const nft_graph = {
  all: nftActivity_all,
  eventType: nftActivity_eventType,
  search: nftText_search,
}

export const ponder_collectionNft = `query SearchNftQuery(
  $after: String
  $limit: Int = 20
  $orderBy: String
  $orderDirection: String
  $contractAddress: String
  $platformNot: String
  $contractAddressNotIn: [String]
  $chainId: Int
  $price_lte: BigInt
  $price_gte: BigInt
  $priceEndTime_gt: BigInt
  $tokenId_contains: String
  $priceEndTime_lt: BigInt
  $price_not: BigInt
) {
  nfts(
    after: $after
    limit: $limit
    orderBy: $orderBy
    orderDirection: $orderDirection
    where: {
      contractAddress: $contractAddress
      platform_not: $platformNot
      contractAddress_not_in: $contractAddressNotIn
      isBurn: false
      chainId: $chainId
      price_lte: $price_lte
      price_gte: $price_gte
      priceEndTime_gt: $priceEndTime_gt
      tokenId_contains: $tokenId_contains
      priceEndTime_lt: $priceEndTime_lt
      price_not: $price_not
    }
  ) {
    items {
      createTime
      isBurn
      level
      priceEndTime
      priceStartTime
      tokenId
      tokenURI
      offers(where: {isCancel: false, price_gt: "0"}) {
        items {
          id
          price
          startTime
          endTime
        }
      }
      price
      ownerId
      chainId
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
  }
}
`

export const ponder_userNft = `query UserNftQuery(
  $after: String
  $limit: Int = 20
  $orderBy: String
  $orderDirection: String
  $contractAddress: String
  $chainId: Int
  $price_lte: BigInt
  $price_gte: BigInt
  $priceEndTime_gt: BigInt
  $tokenId_contains: String
  $priceEndTime_lt: BigInt
  $ownerId: String
  $price_not: BigInt
  $level:Int
  $contractAddress1: String
) {
  nfts(
    after: $after
    limit: $limit
    orderBy: $orderBy
    orderDirection: $orderDirection
    where: {
      OR: [
        {
          contractAddress: $contractAddress
          isBurn: false
          chainId: $chainId
          price_lte: $price_lte
          price_gte: $price_gte
          priceEndTime_gt: $priceEndTime_gt
          tokenId_contains: $tokenId_contains
          priceEndTime_lt: $priceEndTime_lt
          ownerId: $ownerId
          price_not: $price_not
          level: $level
        }
        {
          contractAddress: $contractAddress1
          isBurn: false
          chainId: $chainId
          price_lte: $price_lte
          price_gte: $price_gte
          priceEndTime_gt: $priceEndTime_gt
          tokenId_contains: $tokenId_contains
          priceEndTime_lt: $priceEndTime_lt
          ownerId: $ownerId
          price_not: $price_not
          level: $level
        }
      ]
    }
  ) {
    items {
      createTime
      isBurn
      level
      priceEndTime
      priceStartTime
      tokenId
      tokenURI
      contractAddress
      isLocked
      offers(where: {isCancel: false, price_gt: "0"}) {
        items {
          id
          price
          startTime
          endTime
        }
      }
      price
      ownerId
      chainId
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
  }
}
`

export const ponder_userNft_1 = `query UserNftQuery(
  $after: String
  $limit: Int = 20
  $orderBy: String
  $orderDirection: String
  $contractAddress: String
  $chainId: Int
  $price_lte: BigInt
  $price_gte: BigInt
  $priceEndTime_gt: BigInt
  $tokenId_contains: String
  $priceEndTime_lt: BigInt
  $ownerId: String
  $price_not: BigInt
  $level:Int
) {
  nfts(
    after: $after
    limit: $limit
    orderBy: $orderBy
    orderDirection: $orderDirection
    where: {
          contractAddress: $contractAddress
          isBurn: false
          chainId: $chainId
          price_lte: $price_lte
          price_gte: $price_gte
          priceEndTime_gt: $priceEndTime_gt
          tokenId_contains: $tokenId_contains
          priceEndTime_lt: $priceEndTime_lt
          ownerId: $ownerId
          price_not: $price_not
          level: $level
        }
  ) {
    items {
      createTime
      isBurn
      level
      priceEndTime
      priceStartTime
      tokenId
      tokenURI
      contractAddress
      offers(where: {isCancel: false, price_gt: "0"}) {
        items {
          id
          price
          startTime
          endTime
        }
      }
      price
      ownerId
      chainId
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
  }
}
`
export const ponder_homeNft = `query NftQuery(
  $after: String
  $limit: Int = 10
  $orderBy: String
  $orderDirection: String
  $contractAddress: String
) {
  nfts(
    after: $after
    limit: $limit
    orderBy: $orderBy
    orderDirection: $orderDirection
    where: { contractAddress: $contractAddress, isBurn: false }
  ) {
    items {
      createTime
      isBurn
      level
      ownerId
      price
      priceEndTime
      priceStartTime
      tokenId
      tokenURI
      chainId
      isLocked
      offers(where: {isCancel: false, price_gt: "0"}) {
        items {
          id
          price
          startTime
          from
          endTime
        }
      }
    }
  }
}`
