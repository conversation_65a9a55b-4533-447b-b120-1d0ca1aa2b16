import React, { memo, useEffect, useMemo, useRef, useState } from 'react'
import { LuChevronLeft } from 'react-icons/lu'
import { TbFilter } from 'react-icons/tb'
import { useInView } from 'react-intersection-observer'

import { useApolloClient } from '@apollo/client'
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query'
import { useTranslate } from '@tolgee/react'
import { usePathname } from 'next/navigation'
import { twMerge } from 'tailwind-merge'

import EmptyComp from '@/components/EmptyComp'
import { FilterContext, IFilterContext } from '@/components/Filters/FilterContext'
import NftFilterPanel, { IFilterPanel } from '@/components/Filters/FilterPanel/index-studio'
import Button from '@/components/library/Button'

import studioStore from '@/store/studio-store'

import ActivitySkeleton from '../../library/Skeleton/ActivitySkeleton'
import { ActivityQueryForStudioDocument } from '@/graphql/generated'
import useElectionAddress from '@/hooks/contract/address/useElectionAddress'
import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
import { useReadActivitySearchParams, useUpdateSearchParams } from '@/hooks/useUpdateSearchParams'
import { useAppKitAccount } from '@/libs/contract/wagmiConf'
import { IAddress } from '@/types/IAddress'
import { IGraphActivity } from '@/types/IGraph'

import ActivityItem, { IEventType } from './ActivityItem'

interface IProps {
  className?: {
    base?: string
    btn?: string
  }
  graphQl: {
    all: string
    eventType: string
  }
  type: 'collection' | 'user' | 'nft'
  filterBtn?: boolean
  tokenId?: string
  otherAddress?: IAddress | string
  topVariables?: {
    fromId?: IAddress | string
    tokenId?: string
    toId?: IAddress | string
  }
}
const filterOptions: IFilterPanel[] = ['eventType']
const Activity = (props: IProps) => {
  const updateSearchParams = useUpdateSearchParams('activity')
  const readSearchParams = useReadActivitySearchParams('activity')
  const urlParams = readSearchParams()
  const pathName = usePathname()
  const client = useApolloClient()
  const queryClient = useQueryClient()
  const containerRef = useRef(null)
  const { address: ownerAddress } = useAppKitAccount()
  const { className, type, filterBtn = true, otherAddress, topVariables } = props
  const address = type == 'user' ? otherAddress : ownerAddress
  const fusion = useFusionAddress()
  const election = useElectionAddress()
  const { address: studioAddress } = studioStore((state) => state)
  const eventTypeIn: string[] = useMemo(() => {
    const eventTypeMap = {
      [fusion]: ['MINT', 'TRANSFER', 'LOCK', 'REVERT'],
      [election]: ['MINT', 'TRANSFER'],
    }
    return eventTypeMap[studioAddress] || []
  }, [fusion, studioAddress, election])

  const { ref, inView } = useInView({
    threshold: 0.1, // Trigger when fully visible
  })
  const [filterParams, setFilterParams] = useState<any>(() => {
    return urlParams.eventType ? { event: urlParams.eventType } : {}
  })
  const [variables, setVariables] = useState<{
    after?: string | null
    eventType?: string | null
  } | null>(() => {
    if (urlParams.eventType || urlParams.after) {
      return { eventType: urlParams.eventType, after: urlParams.after }
    }
    return null
  })

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery({
    queryKey: [`${studioAddress}activities`, address, variables, studioAddress, eventTypeIn],
    queryFn: async ({ pageParam = null }) => {
      const addresses =
        studioAddress === fusion
          ? {
              contractAddress: fusion.toLocaleLowerCase(),
            }
          : {
              contractAddress: election.toLocaleLowerCase(),
            }

      console.log(variables)
      const common_filter = {
        query: ActivityQueryForStudioDocument, // GraphQL 查询
        variables: {
          ...topVariables,
          ...variables,
          ...addresses,
          ownerId: address,
          after: pageParam || null,
          limit: 10,
          orderBy: 'createTime',
          orderDirection: 'desc',
          eventTypeIn: eventTypeIn,
        },
      }
      const { data } = await client.query(common_filter)
      return data.nftEvents // 返回数据，React Query 会处理分页
    },
    initialPageParam: null,
    getNextPageParam: (lastPage) => lastPage.pageInfo.endCursor || null,
    // enabled: !!address && !!studioAddress,
    enabled: !!studioAddress,
  })
  useEffect(() => {
    const urlUpdateParams: any = { ...variables, type: 'activity' }
    updateSearchParams(urlUpdateParams)
  }, [variables])
  useEffect(() => {
    function handleClearCache() {
      client.resetStore()
    }
    return () => {
      handleClearCache()
    }
  }, [client, variables, address, pathName])

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])

  useEffect(() => {
    if (filterParams.event) {
      if (filterParams.event === 'all') {
        setVariables({
          after: null,
        })
      } else {
        setVariables({
          after: null,
          eventType: filterParams.event,
        })
      }
    }
  }, [filterParams])
  useEffect(() => {
    if (variables) {
      queryClient.invalidateQueries({ queryKey: [`${studioAddress}activities`, address, variables, studioAddress] })
    }
  }, [variables, studioAddress])
  const { t } = useTranslate(['common', 'profile'])
  const [isFilter, setIsFilter] = useState<boolean>(false)
  const filterContext: IFilterContext = {
    changeFilter: setIsFilter,
    isFilter,
    filterParams,
    changeFilterParams: setFilterParams,
  }
  return (
    <FilterContext.Provider value={filterContext}>
      <div className={twMerge('relative flex h-full flex-col gap-4 py-4', className?.base)}>
        {filterBtn && (
          <div
            className={twMerge(
              'flex flex-row flex-wrap content-start items-center gap-x-4 gap-y-6 px-0 pb-0 pt-2',
              className?.btn
            )}
          >
            <Button
              className="bg-alphaWhite80 text-primary backdrop-blur-[20px] studio-md:bg-buttonTertiary "
              onClick={() => setIsFilter?.(!isFilter)}
            >
              {isFilter ? <LuChevronLeft size={20} /> : <TbFilter size={20} />}
              <span className=" text-sm font-semibold">{t('common_filter', { ns: 'common' })}</span>
            </Button>
          </div>
        )}

        <div className="flex h-full w-full flex-row gap-3" ref={containerRef}>
          {isFilter && <NftFilterPanel onClose={() => setIsFilter(false)} options={filterOptions} className="mt-0" />}
          {!isLoading && data?.pages?.[0]?.items?.length === 0 && (
            <div className="flex h-full w-full items-center justify-center rounded-2xl bg-alphaWhite80 studio-md:bg-[transparent]">
              <EmptyComp title={t('nothing_yet', { ns: 'studio' })} content={t('nft_tips', { ns: 'studio' })} />
            </div>
          )}
          <div
            className={twMerge(
              'flex w-full flex-col gap-4 px-0 pb-0 pt-0',
              !isLoading && data?.pages?.[0]?.items?.length === 0 && 'hidden'
            )}
          >
            {data?.pages.map((page, pageIndex) =>
              page.items.map((item: IGraphActivity, index: number) => (
                <ActivityItem
                  key={`${pageIndex}-${index}-${item.eventType}`}
                  type={item.eventType as IEventType}
                  item={item}
                />
              ))
            )}
            {isLoading && (
              <div className="flex w-full flex-col gap-4">
                {new Array(10).fill('')?.map((_, index) => <ActivitySkeleton key={index} />)}
              </div>
            )}
            <div ref={ref}>
              {isFetchingNextPage ? (
                <p className="h-[1px] w-full" />
              ) : hasNextPage ? (
                <p className="h-[1px] w-full" />
              ) : (
                <p className="h-[1px] w-full" />
              )}
            </div>
          </div>
        </div>
      </div>
    </FilterContext.Provider>
  )
}

export default memo(Activity)
