import React, { memo, useEffect, useRef, useState } from 'react'
import { LuChevronLeft } from 'react-icons/lu'
import { TbFilter } from 'react-icons/tb'
import { useInView } from 'react-intersection-observer'

import { useApolloClient } from '@apollo/client'
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query'
import { useTranslate } from '@tolgee/react'
import { twMerge } from 'tailwind-merge'

import ErrorAndTryComp from '@/components/ErrorAndTryComp'
import { FilterContext, IFilterContext } from '@/components/Filters/FilterContext'
import NftFilterPanel, { IFilterPanel } from '@/components/Filters/FilterPanel'
import Button from '@/components/library/Button'

import ActivitySkeleton from '../library/Skeleton/ActivitySkeleton'
// import { ActivityQueryDocument } from '@/graphql/generated'
// import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
// import useFusionProtocolAddress from '@/hooks/contract/address/useFusionProtocolAddress'
// import useLinkProtocolAddress from '@/hooks/contract/address/useLinkProtocolAddress'
// import useLockNftAddress from '@/hooks/contract/address/useLockNftAdrress'
import useAlgChain from '@/hooks/useAlgChain'
import useBaseChain from '@/hooks/useBaseChain'
import useBSCChain from '@/hooks/useBSCChain'
import useRouter from '@/hooks/useRouter'
import { useReadActivitySearchParams, useUpdateSearchParams } from '@/hooks/useUpdateSearchParams'
import { useAppKitAccount } from '@/libs/contract/wagmiConf'
// import { toLowerCaseLettersOnly } from '@/libs/utils/util'
import { IAddress } from '@/types/IAddress'
import { IGraphActivity } from '@/types/IGraph'

import ActivityItem, { IEventType } from './ActivityItem'
import { SearchContractActivity, SearchNftActivity, SearchOwnerActivity } from './graphql'

interface IProps {
  className?: {
    base?: string
    btn?: string
  }
  graphQl: {
    all: string
    eventType: string
  }
  type: 'collection' | 'user' | 'nft'
  filterBtn?: boolean
  tokenId?: string
  otherAddress?: IAddress | string
  topVariables?: {
    ownerId?: IAddress | string
    fromId?: IAddress | string
    tokenId?: string
    toId?: IAddress | string
    contractAddress?: IAddress | string
    contractAddress1?: IAddress | string
    contractAddress2?: IAddress | string
    contractAddress_in?: IAddress[] | string[]
  }
}
const eventTypeIn = ['BURN', 'LISTING', 'MAKE_OFFER', 'MINT', 'OFFER_CANCEL', 'PURCHASE', 'TRANSFER', 'UNLISTING']

const filterOptions: IFilterPanel[] = ['eventType']
const Activity = (props: IProps) => {
  const updateSearchParams = useUpdateSearchParams('activity')
  const readSearchParams = useReadActivitySearchParams('activity')
  const urlParams = readSearchParams()
  const client = useApolloClient()
  const queryClient = useQueryClient()
  const containerRef = useRef(null)
  const { address: ownerAddress } = useAppKitAccount()
  const { className, type, filterBtn = true, otherAddress, topVariables } = props
  const algChain = useAlgChain()
  const baseChain = useBaseChain()
  const bscChain = useBSCChain()
  const address = type == 'user' ? otherAddress : ownerAddress
  const [eventType, setEventType] = useState<string[]>(eventTypeIn)
  // const fusion = useFusionAddress()
  // const oldfusionProtocol = useFusionProtocolAddress()
  // const fusionProtocol = useLinkProtocolAddress()
  // const lockConstract = useLockNftAddress()

  const { ref, inView } = useInView({
    threshold: 1, // Trigger when fully visible
  })
  const [filterParams, setFilterParams] = useState<any>(() => {
    return urlParams.eventType ? { event: urlParams.eventType } : {}
  })
  const [variables, setVariables] = useState<{
    after?: string | null
    eventType?: string | null
  } | null>(() => {
    if (urlParams.eventType || urlParams.after) {
      return { eventType: urlParams.eventType, after: urlParams.after }
    }
    return null
  })

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery({
    queryKey: ['activities', address, variables],
    queryFn: async ({ pageParam = null }) => {
      console.log(variables)

      const common_filter = {
        query: type == 'nft' ? SearchNftActivity : type == 'user' ? SearchOwnerActivity : SearchContractActivity, // GraphQL 查询
        variables: {
          ...variables,
          after: pageParam || null,
          limit: 10,
          // contractAddress: toLowerCaseLettersOnly(fusion),
          // contractAddress1: toLowerCaseLettersOnly(fusionProtocol),
          // contractAddress2: toLowerCaseLettersOnly(oldfusionProtocol),
          // contractAddress3: toLowerCaseLettersOnly(lockConstract),
          orderBy: 'createTime',
          orderDirection: 'desc',
          eventTypeIn: eventType,
          chainId: Number(algChain),
          chainId_in: [Number(algChain), Number(baseChain), Number(bscChain)],
          ...topVariables,
        },
      }
      const { data } = await client.query(common_filter)

      return data.nftEvents // 返回数据，React Query 会处理分页
    },
    initialPageParam: null,
    getNextPageParam: (lastPage) => lastPage.pageInfo.endCursor || null,
    // enabled: !!address,
  })

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])
  useEffect(() => {
    if (filterParams.event) {
      if (filterParams.event === 'all') {
        setVariables({
          after: null,
        })
        setEventType(eventTypeIn)
      } else {
        setVariables({
          after: null,
          eventType: filterParams.event,
        })
        setEventType([filterParams.event])
      }
    }
  }, [filterParams])
  useEffect(() => {
    const urlUpdateParams: any = { ...variables, type: 'activity' }
    updateSearchParams(urlUpdateParams)
  }, [variables])
  useEffect(() => {
    if (variables) {
      queryClient.invalidateQueries({ queryKey: ['activities', address, variables] })
    }
  }, [variables])
  const { t } = useTranslate(['common', 'profile'])
  const { lngPush } = useRouter()
  const [isFilter, setIsFilter] = useState<boolean>(false)
  const filterContext: IFilterContext = {
    changeFilter: setIsFilter,
    isFilter,
    filterParams,
    changeFilterParams: setFilterParams,
  }
  return (
    <FilterContext.Provider value={filterContext}>
      <div className={twMerge('flex flex-col gap-6 py-4', className?.base)}>
        {filterBtn && (
          <div
            className={twMerge(
              'flex flex-row flex-wrap content-start items-center gap-x-4 gap-y-6 px-0 pb-0 pt-2',
              className?.btn
            )}
          >
            <Button color="gray" onClick={() => setIsFilter?.(!isFilter)}>
              {isFilter ? <LuChevronLeft size={20} /> : <TbFilter size={20} />}
              <span className=" text-sm font-semibold">{t('common_filter', { ns: 'common' })}</span>
            </Button>
          </div>
        )}
        <div className="flex w-full flex-row gap-3" ref={containerRef}>
          {isFilter && <NftFilterPanel onClose={() => setIsFilter(false)} options={filterOptions} className="mt-0" />}
          {!isLoading && data?.pages?.[0]?.items?.length === 0 && (
            <ErrorAndTryComp
              title={t('nothing_yet', { ns: 'profile' })}
              content={t('activity_tips', { ns: 'profile' })}
              buttonStr={t('explore_fusion', { ns: 'profile' })}
              onAction={() => lngPush('/')}
            />
          )}
          <div
            className={twMerge(
              'flex w-full flex-col gap-4 px-0 pb-0 pt-1',
              !isLoading && data?.pages?.[0]?.items?.length === 0 && 'hidden'
            )}
          >
            {data?.pages.map((page, pageIndex) =>
              page.items.map((item: IGraphActivity, index: number) => (
                <ActivityItem key={`${pageIndex}-${index}`} type={item.eventType as IEventType} item={item} />
              ))
            )}
            {isLoading && (
              <div className="flex w-full flex-col gap-4">
                {new Array(10).fill('')?.map((_, index) => <ActivitySkeleton key={index} />)}
              </div>
            )}
            <div ref={ref}>{isFetchingNextPage ? <p /> : hasNextPage ? <p /> : <p />}</div>
          </div>
        </div>
      </div>
    </FilterContext.Provider>
  )
}

export default memo(Activity)
