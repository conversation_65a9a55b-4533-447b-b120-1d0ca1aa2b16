import React, { memo, useContext, useEffect, useRef } from 'react'
import { LuArrowLeft, LuChevronLeft } from 'react-icons/lu'

import { Accordion, AccordionItem } from '@nextui-org/react'
import { useTranslate } from '@tolgee/react'
import dayjs from 'dayjs'
import { twMerge } from 'tailwind-merge'
import { parseUnits } from 'viem'

import Button from '@/components/library/Button'

import useIsPhone from '@/hooks/useIsPhone'

import BlockChainItem from '../BlockChainItem'
import CollectionFilters from '../CollectionItem'
import EventTypeItem from '../EventTypeItem'
import { FilterContext } from '../FilterContext'
import FilterPrice from '../FilterPriceItem'
import FloorPriceItem from '../FloorPriceItem'
import MarketplaceItem from '../MarketplaceItem'
import PropertiesItem from '../PropertiesItem'
import PumpBlockChainItem from '../PumpBlockChainItem'
import StatusItem from '../StatusItem'
import TypeItem from '../TypeItem'
import './style.css'

export type IFilterPanel =
  | 'blockchain'
  | 'status'
  | 'price'
  | 'type'
  | 'collection'
  | 'floorPrice'
  | 'marketplace'
  | 'properties'
  | 'eventType'
  | 'pumpblockchain'
interface INftFiltersProps extends React.HTMLAttributes<HTMLDivElement> {
  onClose: () => void
  options?: IFilterPanel[]
  type?: string
  cleanParams?: () => void
}
const FilterPanel = (props: INftFiltersProps) => {
  const scrollableRef = useRef<HTMLDivElement | null>(null)
  const { filterParams, changeFilterParams, graphParams, changeGraphParams } = useContext(FilterContext)
  const { onClose, className, options = ['blockchain', 'status', 'price', 'collection'], cleanParams } = props
  const { t } = useTranslate(['common', 'filters'])
  const isPhone = useIsPhone()
  const renderPanelItem = (key: IFilterPanel) => {
    switch (key) {
      case 'blockchain':
        return <BlockChainItem />
      case 'status':
        return <StatusItem />
      case 'price':
        return <FilterPrice />
      case 'type':
        return <TypeItem />
      case 'collection':
        return <CollectionFilters namespace={props.type} />
      case 'floorPrice':
        return <FloorPriceItem />
      case 'marketplace':
        return <MarketplaceItem />
      case 'properties':
        return <PropertiesItem />
      case 'eventType':
        return <EventTypeItem />
      case 'pumpblockchain':
        return <PumpBlockChainItem />
      default:
        return null
    }
  }
  const renderPanelTitle = (item: IFilterPanel, param: any) => {
    if (item === 'collection' && param?.collection) {
      return t('collection_traits', { ns: 'filters' })
    }
    return t(item, { ns: 'filters' })
  }
  useEffect(() => {
    if (scrollableRef.current && isPhone) {
      const handleWheel = (e: { deltaY: number; preventDefault: () => void }) => {
        const el = scrollableRef.current
        if (!el) return
        if (
          (e.deltaY < 0 && el.scrollTop === 0) ||
          (e.deltaY > 0 && el.scrollTop + el.clientHeight >= el.scrollHeight)
        ) {
          e.preventDefault()
        }
      }

      const current = scrollableRef.current
      current.addEventListener('wheel', handleWheel)

      return () => {
        current.removeEventListener('wheel', handleWheel)
      }
    }
  }, [scrollableRef.current, isPhone])
  const applyFn = () => {
    if (filterParams) {
      changeFilterParams?.({ ...filterParams })
      const updatedGraphParams = { ...graphParams }
      if (filterParams?.price) {
        const { minValue, maxValue } = filterParams.price
        // min price
        if (minValue !== undefined && minValue) {
          updatedGraphParams.price_gte = parseUnits(minValue.toString(), 18)?.toString()
        }

        // max price
        if (maxValue !== undefined && maxValue) {
          updatedGraphParams.price_lte = parseUnits(maxValue.toString(), 18)?.toString()
        }
        if (minValue === '') {
          delete updatedGraphParams.price_gte
        }
        if (maxValue === '') {
          delete updatedGraphParams.price_lte
        }
      }
      if (filterParams?.status) {
        const currentTimestampInSeconds = dayjs().unix().toString()
        const { status } = filterParams
        switch (status) {
          case 'all':
            delete updatedGraphParams.price_gt
            delete updatedGraphParams.priceEndTime_gt
            delete updatedGraphParams.or
            break
          case 'buy':
            delete updatedGraphParams.or
            updatedGraphParams.price_gt = '0'
            updatedGraphParams.priceEndTime_gt = currentTimestampInSeconds
            break
          case 'noSale':
            delete updatedGraphParams.price_gt
            delete updatedGraphParams.priceEndTime_gt
            updatedGraphParams.or = [
              {
                priceEndTime: null,
              },
              {
                priceEndTime_lte: currentTimestampInSeconds,
              },
            ]
            break
          default:
            break
        }
      }
      changeGraphParams?.({
        ...updatedGraphParams,
        first: 20,
        skip: 0,
      })
    }
    onClose()
  }
  const resetAll = () => {
    if (cleanParams) {
      cleanParams?.()
    } else {
      changeFilterParams?.({})
      changeGraphParams?.({
        first: 20,
        skip: 0,
        orderBy: graphParams?.orderBy || 'price',
        orderDirection: graphParams?.orderDirection || 'desc',
      })
    }
    cleanParams?.()
    onClose()
  }
  return (
    <>
      <div className="fixed left-0 top-0 z-[9999] flex h-[100vh] w-[100vw] bg-alphaWhite80 backdrop-blur-md lg:hidden" />
      <div
        className={twMerge(
          `sm:h-custom top-0 isolate mt-6 flex 
            h-full flex-col items-start overflow-y-auto 
            rounded-xl border-1 border-solid border-borderPrimary 
            bg-alphaWhite100 pb-4 sm:top-4 lg:max-h-[calc(100vh-40px-72px-104px)]
            max-sm:mt-0 max-sm:w-[100vw] max-sm:rounded-none max-lg:fixed 
            max-lg:left-0 max-lg:z-[999999] max-lg:h-screen max-lg:pt-6 max-lg:shadow-2xl `,
          className,
          isPhone ? 'overflow-y-auto overflow-x-hidden pb-4' : ''
        )}
      >
        <div className="hidden w-[374px] flex-row items-center justify-between px-4 pb-2 max-sm:w-full max-lg:flex">
          <div className="text-22 font-semibold text-primary">{t('Filters', { ns: 'common' })}</div>
          <LuArrowLeft className="text-iconTertiary" size={20} onClick={onClose} />
        </div>
        <div className={isPhone ? 'mb-14 w-full overflow-y-auto overflow-x-hidden' : ''} ref={scrollableRef}>
          <Accordion
            keepContentMounted
            className="w-[374px] px-4 max-sm:w-full"
            selectionMode="multiple"
            itemClasses={{
              base: 'border-borderPrimary w-full',
              trigger: 'py-3',
              content: 'pt-0 pb-3',
              // "titleWrapper": 'border-b-1 border-borderPrimary border-solid',
            }}
            showDivider={false}
          >
            {options?.map((item) => (
              <AccordionItem
                key={item}
                aria-label={item}
                title={<div className="text-xl font-semibold text-primary">{renderPanelTitle(item, filterParams)}</div>}
                indicator={<LuChevronLeft size={20} className="text-iconSecondary hover:text-iconPrimary" />}
                className="border-b-1 border-solid border-borderPrimary"
              >
                {renderPanelItem(item)}
              </AccordionItem>
            ))}
          </Accordion>
        </div>
        {isPhone && (
          <div className="item-center absolute bottom-6 mt-6  flex w-full flex-row justify-center gap-2 px-2">
            <Button color="gray" className="w-1/2" onClick={resetAll}>
              {t('reset_all', { ns: 'filters' })}
            </Button>
            <Button className="w-1/2" onClick={applyFn}>
              {t('Apply', { ns: 'common' })}
            </Button>
          </div>
        )}
      </div>
    </>
  )
}

export default memo(FilterPanel)
