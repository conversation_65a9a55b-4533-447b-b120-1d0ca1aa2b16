'use client'

import React, { AnchorHTMLAttributes, Key, memo, useContext, useMemo, useRef } from 'react'
import { LuChevronLeft, LuListFilter, LuRefreshCw } from 'react-icons/lu'
import { TbFilter } from 'react-icons/tb'

import { Switch } from '@nextui-org/react'
import { useTranslate } from '@tolgee/react'
import { twMerge } from 'tailwind-merge'

import CardSizeTab from '@/components/CardSizeTab'
import SearchInput from '@/components/Input/SearchInput'
import Button from '@/components/library/Button'
import Dropdown, { IDropdownItemProps } from '@/components/library/Dropdown'
import Tooltip from '@/components/library/Tooltip'

import { FilterContext } from '../FilterContext'

interface INftFiltersProps extends AnchorHTMLAttributes<HTMLDivElement> {
  onSearch?: (value: string) => void
  onDrowndown?: (value: string | string[]) => void
  refetch?: () => void
  isAlg?: boolean
  defaultTab?: string
  defaultOrder?: string
  defaultSearch?: string
}
const NftFilters = (props: INftFiltersProps) => {
  const {
    className = '',
    onSearch,
    onDrowndown,
    refetch,
    isAlg = true,
    defaultTab,
    defaultOrder,
    defaultSearch,
  } = props
  const { refreshNftSize, isFilter, changeFilter, changeFilterParams, filterParams, graphParams, changeGraphParams } =
    useContext(FilterContext)
  const { t } = useTranslate(['common', 'explore', 'filters'])
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const onSwitchChange = (checked: boolean) => {
    if (checked) {
      timerRef.current = setInterval(() => {
        refetch?.()
      }, 30000)
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }
  const filterDropdownList: IDropdownItemProps[] = useMemo(() => {
    return [
      {
        key: 'trending',
        value: t('trending', { ns: 'common' }),
      },
      {
        key: 'recently',
        value: t('recently', { ns: 'common' }),
      },
      {
        key: 'low',
        value: t('low', { ns: 'common' }),
      },
      {
        key: 'high',
        value: t('high', { ns: 'common' }),
      },
    ]
  }, [t])
  const filterLength = useMemo(() => {
    return (
      Object.keys(filterParams).filter(
        (key) =>
          filterParams[key] !== null &&
          filterParams[key] !== undefined &&
          filterParams[key] !== 'all' &&
          filterParams[key]?.length !== 0
      ).length || 0
    )
  }, [filterParams])
  const clearAll = () => {
    changeFilterParams?.({})
    changeGraphParams?.({
      first: 20,
      skip: 0,
      orderBy: graphParams?.orderBy || 'price',
      orderDirection: graphParams?.orderDirection || 'desc',
    })
  }
  return (
    <div className={twMerge('flex items-center gap-4', className, !isAlg && 'justify-end')}>
      <div className="hidden gap-1.5 lg:flex">
        {isAlg && (
          <Button color="gray" onClick={() => changeFilter?.(!isFilter)} badge={filterLength}>
            {isFilter ? <LuChevronLeft size={20} /> : <TbFilter size={20} />}
            <span className=" text-sm font-semibold">{t('common_filter', { ns: 'common' })}</span>
          </Button>
        )}
      </div>
      {filterLength > 0 && isAlg && (
        <Button color="gray" className="hidden gap-1.5 lg:flex" onClick={clearAll}>
          <span className="text-sm font-semibold">{t('clear_all', { ns: 'filters' })}</span>
        </Button>
      )}
      <div className="hidden items-center gap-1 lg:flex">
        <Switch onValueChange={onSwitchChange} />
        <Tooltip content={t('filter_live_data_tooltip_content', { ns: 'common' })}>
          <div className="flex flex-shrink-0 items-center gap-3">
            <span className=" text-xs text-quaternary">{t('Live_data', { ns: 'explore' })}</span>
            <span className="relative flex h-3 w-3 items-center justify-center">
              <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-success opacity-75"></span>
              <span className="relative inline-flex h-2 w-2 rounded-full bg-success"></span>
            </span>
          </div>
        </Tooltip>
      </div>
      {isAlg && (
        <SearchInput
          placeholder={t('search_nfts', { ns: 'explore' })}
          onSearch={onSearch}
          defaultValue={defaultSearch}
        />
      )}
      <div className="flex items-center gap-1.5">
        {isAlg && (
          <>
            <Button color="gray" className="min-w-10 !p-0 lg:hidden" onClick={() => changeFilter?.(!isFilter)}>
              <TbFilter size={20} className=" text-primary" />
            </Button>
            <Button color="gray" className="min-w-10 !p-0 lg:hidden" onClick={() => refetch?.()}>
              <LuRefreshCw size={20} className=" text-primary" />
            </Button>
            <Button color="gray" className="min-w-10 !p-0 md:hidden">
              <LuListFilter size={20} className=" text-primary" />
            </Button>
            <div className="hidden md:block">
              <Dropdown list={filterDropdownList} onAction={onDrowndown} defaultValue={defaultOrder} />
            </div>
          </>
        )}
        <div className="hidden lg:block">
          <CardSizeTab
            selectedKey={defaultTab}
            handleTab={(key: Key) => {
              refreshNftSize?.(key === 'min')
            }}
          />
        </div>
      </div>
    </div>
  )
}

export default memo(NftFilters)
