import React, { use<PERSON><PERSON>back, useContext, useEffect, useMemo, useState } from 'react'
import { LuChevronLeft, LuX } from 'react-icons/lu'

import { Accordion, AccordionItem, Checkbox, CheckboxGroup } from '@nextui-org/react'
import { useTranslate } from '@tolgee/react'

// import { formatEther } from 'viem'
import SearchInput from '@/components/Input/SearchInput'
import Button from '@/components/library/Button'

import useGetPumpCollection from '@/app/[locale]/(commonLayout)/explore/hooks/useGetPumpCollection'
import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
import useGetAllCollection from '@/hooks/query/collection/useGetAllCollection'
import useGetNftAttrs from '@/hooks/query/nft/useGetNftAttrs'
import { deserializeAttrs, useCollectionPersistence } from '@/hooks/useCollectionPersistence'
import { useReadNftSearchParams } from '@/hooks/useUpdateSearchParams'
import { formatNumber2, formatWithCommas2 } from '@/libs/common/format'
import { IAddress } from '@/types/IAddress'
import { IHomeCollectionData } from '@/types/ITable'

import { FilterContext } from '../FilterContext'

type IAttr = {
  k: string
  v: string[]
}
const CollectionItem = ({ namespace }: { namespace?: string }) => {
  const { t } = useTranslate(['explore', 'common', 'filters'])
  const { collections } = useGetAllCollection()
  const { clearCollectionStorage } = useCollectionPersistence(namespace || 'nfts')
  const readSearchParams = useReadNftSearchParams(namespace || 'nfts')
  const urlParams = readSearchParams()
  const fusion = useFusionAddress()
  const [realCollections, setRealCollections] = useState<IHomeCollectionData[]>([])
  const [pageSize] = useState(10)
  const [currentPage, setCurrentPage] = useState(1)
  const [attrs, setAttrs] = useState<IAttr[]>(() => {
    const attrData = (urlParams.attrs && deserializeAttrs(urlParams.attrs)) || []
    return attrData
  })
  const { filterParams, changeFilterParams } = useContext(FilterContext)
  const [search, setSearch] = useState('')
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [collectionSearch, setCollectionSearch] = useState('')
  const collectionItem: IHomeCollectionData = useMemo(() => {
    const { collection = null } = filterParams
    return collection
  }, [filterParams])
  const { data: nftAttrs } = useGetNftAttrs(collectionItem?.address as IAddress)
  const {
    pumpCollections,
    isSuccess,
    isLoading: isPumpLoading,
    refetch,
  } = useGetPumpCollection(currentPage, pageSize, collectionSearch, filterParams.chainId)

  const loadMoreCollections = useCallback(async () => {
    if (isLoadingMore || !hasMore) return
    setIsLoadingMore(true)
    const nextPage = currentPage + 1
    setCurrentPage(nextPage)
    await refetch()
    setIsLoadingMore(false)
  }, [currentPage, hasMore, isLoadingMore, refetch])

  useEffect(() => {
    setCurrentPage(1)
    refetch()
  }, [filterParams.chainId])
  useEffect(() => {
    let items: IHomeCollectionData[] = [],
      restItems: IHomeCollectionData[] = []

    if (collections.length > 0) {
      items = collections.filter(
        (item) => item.address.toLowerCase() === fusion.toLowerCase()
      ) as unknown as IHomeCollectionData[]
      if (collectionSearch) {
        items = items.filter((item) => item?.name?.toLowerCase().indexOf(collectionSearch.toLowerCase()) !== -1)
      }
    }

    if (isSuccess && pumpCollections.length > 0) {
      restItems = pumpCollections as unknown as IHomeCollectionData[]
      if (restItems.length < pageSize) {
        setHasMore(false)
      } else {
        setHasMore(true)
      }
    }

    if (filterParams.chainId && filterParams.chainId !== 'all') {
      items = items.filter((item) => Number(item.chainId) === Number(filterParams.chainId))
      restItems = restItems.filter((item) => Number(item.chainId) === Number(filterParams.chainId))
    }

    if (currentPage === 1) {
      const next = [...items, ...restItems]
      setRealCollections(next)
    } else {
      setRealCollections((prev) => {
        const existingAddresses = new Set(prev.map((item) => item.address))
        const newItems = restItems.filter((item) => !existingAddresses.has(item.address))
        return [...prev, ...newItems]
      })
    }
  }, [collections, pumpCollections, isSuccess, fusion, pageSize, currentPage, collectionSearch, filterParams.chainId])

  const changeCollection = (value: string) => {
    setCollectionSearch(value)
    setCurrentPage(1)
  }
  const changeCollectionItem = (item: IHomeCollectionData) => {
    changeFilterParams?.({
      ...filterParams,
      collection: item,
    })
  }
  const deleteCollection = () => {
    clearCollectionStorage()
    changeFilterParams?.({
      ...filterParams,
      collection: null,
      attrs: [],
    })
  }
  useEffect(() => {
    if (attrs.length > 0) {
      changeFilterParams?.({
        ...filterParams,
        attrs,
      })
    } else {
      changeFilterParams?.({
        ...filterParams,
        attrs: [],
      })
    }
  }, [attrs])

  const renderPanelItem = useCallback(
    (items: string[], key: string) => {
      const filteredItems = items.filter((item) => item && item.toLowerCase().includes(search.toLowerCase()))
      const selectedValues = filterParams?.attrs?.find((attr: { k: string }) => attr.k === key)?.v || []

      return (
        <CheckboxGroup
          className="text-sm font-medium text-quaternary"
          value={selectedValues}
          onChange={(value: string[]) => {
            const updatedAttrs = attrs.filter((attr) => attr.k !== key)
            if (value.length > 0) {
              updatedAttrs.push({ k: key, v: value })
            }
            setAttrs(updatedAttrs)
          }}
        >
          {filteredItems.map((item) => {
            if (item) {
              return (
                <Checkbox value={item} key={item}>
                  {item}
                </Checkbox>
              )
            }
          })}
        </CheckboxGroup>
      )
    },
    [attrs, filterParams?.attrs, search]
  )
  const onSearchAction = (value: string) => {
    setSearch(value)
  }
  const filterItems = (items: any) => {
    if (Array.isArray(items)) {
      return items.map((item) => {
        if (item) {
          return item
        }
      })
    }
    return []
  }
  return (
    <div className="flex flex-col gap-4 px-0 pb-0">
      {collectionItem?.address && (
        <div className="flex w-full flex-row items-start gap-[0.875rem] rounded-xl bg-buttonTertiary p-[0.875rem]">
          <img src={collectionItem?.logo} className="h-[2.5rem] w-[2.5rem] flex-shrink-0 rounded-md" />
          <div className="flex flex-grow flex-col items-start gap-1 p-0">
            <div className="flex w-full flex-row items-center justify-between gap-3 p-0">
              <div className="w-[131px] flex-grow overflow-hidden text-ellipsis whitespace-nowrap text-sm font-medium text-primary">
                {collectionItem?.name}
              </div>
              <div className="flex-shrink-0 whitespace-nowrap text-sm font-medium text-primary">
                {formatWithCommas2(formatNumber2(collectionItem?.tokenPrice ?? 0, 'symbol', '$') as number | string)} $
              </div>
            </div>
            <div className="flex w-full flex-row items-center justify-between gap-3 p-0">
              <div className="text-xs font-medium text-quaternaryInvert">
                {t('floor', { ns: 'common' })}:{' '}
                {`${collectionItem?.floorPrice} ${collectionItem.address.toLowerCase() === fusion.toLowerCase() ? 'ALG' : ''}`}
              </div>
              <div className="text-xs font-medium text-quaternaryInvert">7d</div>
            </div>
          </div>
          <div
            className="flex h-[40px] w-[40px] flex-shrink-0 cursor-pointer items-center justify-center rounded-xl p-2 text-iconTertiary hover:bg-buttonPrimary4"
            onClick={deleteCollection}
          >
            <LuX size={24} />
          </div>
        </div>
      )}
      {filterParams?.collection ? (
        <SearchInput
          placeholder={t('search_by_traits', { ns: 'filters' })}
          onSearch={onSearchAction}
          debounceTime={500}
        />
      ) : (
        <SearchInput
          placeholder={t('search_by_collections', { ns: 'explore' })}
          onSearch={changeCollection}
          debounceTime={500}
        />
      )}
      {!collectionItem &&
        realCollections?.map((item, index) => (
          <div
            className="flex cursor-pointer flex-row items-start gap-3 p-0"
            key={`${index}_${item?.name}`}
            onClick={() => changeCollectionItem(item)}
          >
            <img src={item?.logo} className="h-[2.5rem] w-[2.5rem] rounded-md" />
            <div className="flex w-full flex-col items-start gap-1 p-0">
              <div className="flex w-full flex-row items-center justify-between gap-3 p-0">
                <div className="text-sm font-medium text-primary">{item?.name}</div>
                <div className="text-nowrap text-sm font-medium text-primary">
                  {formatWithCommas2(formatNumber2(item?.tokenPrice ?? 0, 'symbol', '$') as number | string)} $
                </div>
              </div>
              <div className="flex w-full flex-row items-center justify-between gap-3 p-0">
                <div className="text-xs font-medium text-quaternaryInvert">
                  {t('floor', { ns: 'common' })}:{' '}
                  {`${item?.floorPrice} ${item.address.toLowerCase() === fusion.toLowerCase() ? 'ALG' : ''}`}
                </div>
                <div className="text-xs font-medium text-quaternaryInvert">7d</div>
              </div>
            </div>
          </div>
        ))}
      {collectionItem?.address && (
        <Accordion
          keepContentMounted
          className="max-sm:w-full"
          selectionMode="multiple"
          itemClasses={{ base: 'border-borderPrimary w-full' }}
        >
          {nftAttrs?.data &&
            Object.entries(nftAttrs.data)
              .filter(
                ([key, items]) =>
                  key.toLowerCase().includes(search.toLowerCase()) ||
                  filterItems(items).some((item) => item.toLowerCase().includes(search.toLowerCase()))
              )
              .map(([key, items]) => {
                if (key !== 'unique_index' && key !== 'asset_id') {
                  return (
                    <AccordionItem
                      key={key}
                      aria-label={key}
                      title={<div className="text-xl font-semibold text-primary">{key}</div>}
                      indicator={<LuChevronLeft size={20} className="text-iconTertiary" />}
                    >
                      {renderPanelItem(filterItems(items), key)}{' '}
                    </AccordionItem>
                  )
                }
              })}
        </Accordion>
      )}
      {hasMore && !collectionItem?.address && (realCollections.length > 0 || isLoadingMore || isPumpLoading) && (
        <Button
          color="gray"
          onClick={loadMoreCollections}
          isLoading={isLoadingMore || isPumpLoading}
          disabled={isLoadingMore || isPumpLoading}
        >
          {t('load_more', { ns: 'filters' })}
        </Button>
      )}
    </div>
  )
}

export default CollectionItem
