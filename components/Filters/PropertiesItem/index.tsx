import React, { useCallback, useContext, useEffect, useState } from 'react'
import { LuChevronLeft } from 'react-icons/lu'

import { Accordion, AccordionItem, Checkbox, CheckboxGroup } from '@nextui-org/react'
import { useTranslate } from '@tolgee/react'
import { useParams } from 'next/navigation'

import SearchInput from '@/components/Input/SearchInput'

// import supportCollections from '@/constant/collection'
// import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
import useGetNftAttrs from '@/hooks/query/nft/useGetNftAttrs'
import { deserializeAttrs } from '@/hooks/useCollectionPersistence'
import { useReadNftSearchParams } from '@/hooks/useUpdateSearchParams'
// import { supportChainId } from '@/libs/contract/wagmiConf'
import { IAddress } from '@/types/IAddress'

import { FilterContext } from '../FilterContext'

type IAttr = {
  k: string
  v: string[]
}

const PropertiesItem = () => {
  const { t } = useTranslate('filters')
  const params = useParams()
  const readSearchParams = useReadNftSearchParams('nfts')
  const urlParams = readSearchParams()
  // const isFusion =
  //   supportCollections.find((item) => item.contractAddress === params.address)?.chainId === supportChainId
  const { data: nftAttrs } = useGetNftAttrs(params.address as IAddress)
  const [attrs, setAttrs] = useState<IAttr[]>(() => {
    const attrData = (urlParams.attrs && deserializeAttrs(urlParams.attrs)) || []
    return attrData
  })
  const [search, setSearch] = useState('')
  const { filterParams, changeFilterParams } = useContext(FilterContext)
  const renderPanelItem = useCallback(
    (items: any, key: string) => {
      const filteredItems = items.filter((item: any) => item && item.toLowerCase().includes(search.toLowerCase()))
      const selectedValues = filterParams?.attrs?.find((attr: { k: string }) => attr.k === key)?.v || []
      return (
        <CheckboxGroup
          value={selectedValues}
          className="text-sm font-medium text-quaternary"
          onChange={(value: string[]) => {
            if (value.length > 0) {
              const updatedAttrs = attrs.filter((attr) => attr.k !== key)
              updatedAttrs.push({ k: key, v: value })
              setAttrs([...updatedAttrs])
            } else {
              setAttrs(attrs.filter((attr) => attr.k !== key))
            }
          }}
        >
          {filteredItems.map((item: any) => (
            <Checkbox value={item} key={item}>
              {item}
            </Checkbox>
          ))}
        </CheckboxGroup>
      )
    },
    [attrs, filterParams, search]
  )
  useEffect(() => {
    if (attrs.length > 0) {
      changeFilterParams?.({
        ...filterParams,
        attrs,
      })
    } else {
      changeFilterParams?.({
        ...filterParams,
        attrs: [],
      })
    }
  }, [attrs])

  const onSearch = (value: string) => {
    setSearch(value)
  }
  const filterItems = (items: any) => {
    if (Array.isArray(items)) {
      return items.map((item) => {
        if (item) {
          return item
        }
      })
    }
    return []
  }
  return (
    <div className="flex w-full flex-col items-start p-0">
      <SearchInput placeholder={t('search_by_traits')} onSearch={onSearch} />
      <Accordion
        keepContentMounted
        className="max-sm:w-full"
        selectionMode="multiple"
        itemClasses={{ base: 'border-borderPrimary w-full' }}
      >
        {nftAttrs?.data &&
          Object.entries(nftAttrs.data)
            .filter(
              ([key, items]) =>
                key.toLowerCase().includes(search.toLowerCase()) ||
                filterItems(items).some((item) => item.toLowerCase().includes(search.toLowerCase()))
            )
            .map(([key, items]) => (
              <AccordionItem
                key={key}
                aria-label={key}
                title={<div className="text-xl font-semibold text-primary">{key}</div>}
                indicator={<LuChevronLeft size={20} className="text-iconTertiary" />}
              >
                {renderPanelItem(filterItems(items), key)}
              </AccordionItem>
            ))}
      </Accordion>
    </div>
  )
}

export default PropertiesItem
