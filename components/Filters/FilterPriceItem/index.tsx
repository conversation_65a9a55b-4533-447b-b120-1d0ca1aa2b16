import React, { memo, useContext, useEffect, useMemo, useState } from 'react'
// import { FaChevronDown } from 'react-icons/fa'
import { HiOutlineCheck } from 'react-icons/hi'
import { LuChevronDown } from 'react-icons/lu'

import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger, Button as Nextbutton } from '@nextui-org/react'
import { useTranslate } from '@tolgee/react'
import { useParams } from 'next/navigation'
import { parseUnits } from 'viem'

import MinToMaxInput from '@/components/Input/MinToMaxInput'
import Button from '@/components/library/Button'

import useGetAllCollection from '@/hooks/query/collection/useGetAllCollection'
import useIsPhone from '@/hooks/useIsPhone'
import { getNativeTokenSymbol, toLowerCaseLettersOnly } from '@/libs/utils/util'
import { IAddress } from '@/types/IAddress'
import { IHomeCollectionData } from '@/types/ITable'

import { FilterContext } from '../FilterContext'

const FilterPrice = () => {
  const { collections, isLoading } = useGetAllCollection()
  const { filterParams, changeFilterParams, graphParams, changeGraphParams } = useContext(FilterContext)
  const params = useParams()
  const { t } = useTranslate('common')
  const [minValue, setMinValue] = useState(filterParams?.price?.minValue || '')
  const [maxValue, setMaxValue] = useState(filterParams?.price?.maxValue || '')
  const [price, setPrice] = useState('ALG')
  const isPhone = useIsPhone()
  useEffect(() => {
    if (!filterParams?.price) {
      setMinValue('')
      setMaxValue('')
    }
  }, [filterParams])
  const onMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMinValue(e.target.value)
  }
  const onMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMaxValue(e.target.value)
  }

  const priceList: IHomeCollectionData[] = useMemo(() => {
    return collections.filter((item, index) => {
      return (
        collections.findIndex(
          (t) => toLowerCaseLettersOnly(t.address) == toLowerCaseLettersOnly(params?.address as IAddress)
        ) === index
      )
    })
  }, [collections, params?.address])

  const actionhandle = (key: any) => {
    setPrice(key)
  }
  const applyPrice = () => {
    // create a copy of graphParams
    changeFilterParams?.({ ...filterParams, price: { minValue, maxValue, symbol: price } })
    if (!isPhone) {
      const updatedGraphParams = { ...graphParams }

      // min price
      if (minValue !== undefined && minValue) {
        updatedGraphParams.price_gte = parseUnits(minValue.toString(), 18)?.toString()
      }

      // max price
      if (maxValue !== undefined && maxValue) {
        updatedGraphParams.price_lte = parseUnits(maxValue.toString(), 18)?.toString()
      }
      if (minValue === '') {
        delete updatedGraphParams.price_gte
      }
      if (maxValue === '') {
        delete updatedGraphParams.price_lte
      }
      // update graphParams
      changeGraphParams?.({
        ...updatedGraphParams,
        first: 20,
        skip: 0,
      })
    }
  }
  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex flex-row gap-[10px]">
        <MinToMaxInput
          minValue={minValue}
          maxValue={maxValue}
          onMaxChange={onMaxChange}
          onMinChange={onMinChange}
          classNames={{
            inputDiv: 'w-[6.5rem]',
          }}
        />

        <Dropdown placement="bottom-start">
          <DropdownTrigger>
            <Nextbutton className="w-full bg-buttonPrimary4" isLoading={isLoading}>
              <div className="flex flex-grow-0 items-center justify-center  gap-1 py-[10px] text-sm font-semibold">
                <span>{price}</span>
                <LuChevronDown className="h-5 w-5 text-alphaBlack100" />
              </div>
            </Nextbutton>
          </DropdownTrigger>
          <DropdownMenu variant="faded" aria-label="footer_theme" onAction={actionhandle}>
            {priceList?.map((item) => (
              <DropdownItem key={`${getNativeTokenSymbol(item.chainId)}`}>
                <div className="flex items-center gap-3 text-sm font-semibold">
                  <span> {getNativeTokenSymbol(item.chainId)}</span>
                  <span className="ml-auto">
                    {getNativeTokenSymbol(item.chainId) === price ? <HiOutlineCheck className="h-5 w-5" /> : null}
                  </span>
                </div>
              </DropdownItem>
            ))}
          </DropdownMenu>
        </Dropdown>
      </div>

      <Button color="gray" className="w-full" onClick={applyPrice}>
        {t('Apply')}
      </Button>
    </div>
  )
}

export default memo(FilterPrice)
