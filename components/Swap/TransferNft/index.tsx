import React, { memo, useContext, useEffect, useMemo, useState } from 'react'
import { useInView } from 'react-intersection-observer'

import { Skeleton } from '@nextui-org/react'
import { useInfiniteQuery } from '@tanstack/react-query'
import { useTranslate } from '@tolgee/react'
import { useParams } from 'next/navigation'
import { twMerge } from 'tailwind-merge'

import Button from '@/components/library/Button'
import Input from '@/components/library/Input'
import NftCard, { INftCard } from '@/components/NftCard'

import { TokenDetailContext } from '@/app/[locale]/(commonLayout)/collection/[chainId]/[address]/[id]/TokenDetailContext'
import { SearchPumpNFTsOwnerStr } from '@/app/[locale]/(commonLayout)/pump404/[chainId]/[address]/graphql'
import useElectionAddress from '@/hooks/contract/address/useElectionAddress'
import useTransferToken from '@/hooks/contract/fusion/useTransferFrom'
import useGetSaleInfo from '@/hooks/contract/fusionProtocol/useGetSaleInfo'
import useRemoveSale from '@/hooks/contract/fusionProtocol/useRemoveSale'
import useSolanaTransferToken from '@/hooks/contract/solana/useSolanaTransferToken'
import useConnect from '@/hooks/useConnect'
import useCurrentChain from '@/hooks/useCurrentChain'
import { isEVMChain } from '@/hooks/useIsEvm'
import { isEVMAddress } from '@/hooks/useSafeAddress'
import { isSolanaChain } from '@/hooks/useSolanaInfo'
// import useOwnedNftList from '@/hooks/useOwnedNftList'
import { useToast } from '@/hooks/useToast'
import { useAppKitAccount, useAppKitNetwork } from '@/libs/contract/wagmiConf'
import { toLowerCaseLettersOnly } from '@/libs/utils/util'
import { tokenSearch as tokenSearchReq } from '@/request/api/nft'
import { ITokenSaleInfo } from '@/types/fusionProtocol'
import { IAddress } from '@/types/IAddress'
import { IChain } from '@/types/IChain'

const TransferNft = () => {
  const { t } = useTranslate('token')
  const param = useParams()
  const id = param?.id as string
  const { chainId: accountChain } = useAppKitNetwork()
  const chainId = param?.chainId as unknown as IChain
  const issolanaChain = isSolanaChain(chainId)
  const { refreshOwner } = useContext(TokenDetailContext)
  const [refreshKey, setRefreshKey] = useState(0)
  const { ref, inView } = useInView({
    threshold: 1, // Trigger when fully visible
  })
  // const queryClient = useQueryClient()
  const election = useElectionAddress()
  const collectionAddress = param?.address as IAddress | string
  const { address } = useAppKitAccount()
  const { withConnect } = useConnect()
  const currentChain = useCurrentChain()
  const { errorToast, successToast } = useToast()
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    refetch: nftsRefetch,
  } = useInfiniteQuery({
    queryKey: ['getTransferMyNft', address, chainId, collectionAddress],
    queryFn: async ({ pageParam = null }) => {
      const realCollectionAddress = issolanaChain ? collectionAddress : collectionAddress?.toLocaleLowerCase()
      let variables: any = {
        after: pageParam || null,
        limit: 20,
        contractAddress: realCollectionAddress,
        chainId: chainId,
      }
      if (address) {
        variables = {
          ...variables,
          ownerId: issolanaChain ? address : address?.toLocaleLowerCase() || null,
        }
      }
      const params: any = {
        query: SearchPumpNFTsOwnerStr,
        variables,
        operationName: 'SearchMyTransferNft',
        contractAddress: realCollectionAddress,
      }
      const { data } = await tokenSearchReq({ ...params })
      const { tokens, pageInfo } = data // const res = await client.query({
      //   query: ownerAddress ? SearchPumpNFTsOwner : SearchPumpNFTs,
      //   variables,
      //   fetchPolicy: 'no-cache',
      // })
      // let tokens = data?.data?.nfts?.items || []
      // tokens = tokens?.map((item: any) => {
      //   if (item?.tokenURI) {
      //     const metadataobj = JSON.parse(item?.tokenURI?.substring(27))
      //     return { ...item, ...metadataobj }
      //   }
      //   return item
      // })

      const newItems = tokens?.map((item: any) => {
        const { attributes = [] } = item
        let level = item?.level || ''
        attributes?.map((item: { trait_type: string; value: any }) => {
          if (item?.trait_type == 'Level') {
            level = item?.value
          }
        })
        return {
          cover: item?.image || '',
          collectionName: '',
          name: item?.name || '',
          // name: `Rabbit #${item.tokenId.toString().slice(0, 5)}`,
          id: item?.tokenId,
          collectionAddress: item?.contractAddress || '',
          price: 0.1,
          highestBid: 0,
          url: item?.tokenURI,
          iframe_url: item?.iframe_url || '',
          nftDeatil: {
            ownerAddress: item?.ownerId,
            price: item?.price,
            priceEndTime: item?.priceEndTime,
            priceStartTime: item?.priceStartTime,
            offers: item?.offers?.items || [],
            chainId: item?.chainId,
          },
          level: level ? (level?.toString()?.startsWith('Lv') ? level : `Lv-${level}`) : '',
        } as INftCard
      })
      return { items: newItems || [], pageInfo: pageInfo || null }
    },
    initialPageParam: null,
    getNextPageParam: (lastPage) => lastPage?.pageInfo?.endCursor || null,
    enabled: !!collectionAddress && !!address,
    staleTime: 100,
    gcTime: 100,
  })
  // const { tokenList, isLoading, ownerRefetch } = useOwnedNftList(collectionAddress)
  const [selectNft, setSelectNft] = useState<string>('')
  const [receiverAddress, setReceiverAddress] = useState<string>('')

  const {
    transferToken,
    isError,
    isLoading: isTransferLoading,
    isSuccess,
    errorMessage,
  } = useTransferToken(
    toLowerCaseLettersOnly(collectionAddress) === toLowerCaseLettersOnly(election) ? 'election' : 'fusion',
    issolanaChain ? '0x' : (collectionAddress as IAddress),
    chainId
  )
  const {
    transferToken: solanaTransferToken,
    isLoading: isSolanaTransferLoading,
    error: solanaTransferError,
  } = useSolanaTransferToken(chainId)

  const { saleInfo: saleData = null, isSuccess: isSaleInfoSuccess } = useGetSaleInfo(
    selectNft,
    currentChain,
    issolanaChain ? '0x' : (collectionAddress as IAddress)
  )
  const [saleInfo, setSaleInfo] = useState<ITokenSaleInfo>({
    seller: '' as IAddress,
    price: 0,
    startTime: 0,
    endTime: 0,
  })
  const {
    removeSale,
    isError: isRemoveError,
    errorInfo,
    isLoading: isRemoveLoading,
    isSuccess: isRemoveSuccess,
  } = useRemoveSale(issolanaChain ? '0x' : (collectionAddress as IAddress))
  const disableTransfer = useMemo(
    () =>
      !receiverAddress ||
      !selectNft ||
      !(
        (isEVMAddress(receiverAddress) && isEVMChain(accountChain) && !issolanaChain) ||
        (!isEVMAddress(receiverAddress) && isSolanaChain(accountChain) && issolanaChain)
      ),
    [receiverAddress, selectNft, accountChain]
  )
  useEffect(() => {
    if (data?.pages && data?.pages?.length > 0 && id) {
      let haveId = ''
      data?.pages?.map((page) => {
        page?.items?.map((item: { id: string }) => {
          if (item?.id === id) {
            haveId = id
          }
        })
      })
      if (haveId) {
        setSelectNft(haveId)
      } else {
        setSelectNft('')
      }
      // const index = tokenList.findIndex((item) => item.id === id)
      // if (index !== -1) {
      //   setSelectNft(id)
      // } else {
      //   setSelectNft('')
      // }
    }
  }, [data, id])
  useEffect(() => {
    if (isSaleInfoSuccess) {
      setSaleInfo({ ...(saleData as ITokenSaleInfo) })
    }
  }, [isSaleInfoSuccess])
  useEffect(() => {
    const hasError = isError || isRemoveError || !!solanaTransferError
    const error = errorMessage || errorInfo || solanaTransferError
    if (hasError) {
      errorToast(error)
    }
  }, [errorMessage, isError, isRemoveError, errorInfo, solanaTransferError])

  useEffect(() => {
    if (isSuccess) {
      successToast(t('swap_transfer_token_success'))
      refreshOwner?.()
      setRefreshKey((prev) => prev + 1)
      setTimeout(() => {
        nftsRefetch()
      }, 3000)
    }
  }, [isSuccess])

  const changeReceiverAddress = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setReceiverAddress(value)
  }
  const handleTransfer = withConnect(async () => {
    try {
      if (issolanaChain) {
        const result = await solanaTransferToken({
          fungibleMint: collectionAddress as string,
          recipient: receiverAddress as string,
          amount: 1,
          chainId,
        })
        if (result.success) {
          successToast(t('swap_transfer_token_success'))
          refreshOwner?.()
          setRefreshKey((prev) => prev + 1)
          setTimeout(() => {
            nftsRefetch()
          }, 3000)
        }
        return
      }
      if (Number(saleInfo?.price) > 0 && saleInfo?.startTime != 0) {
        await removeSale(address as IAddress, id)
      } else {
        await transferToken(address as IAddress, receiverAddress as IAddress, selectNft, 'nft')
      }
    } catch (error) {
      throw error
    }
  })

  useEffect(() => {
    const transfer = async () => {
      if (isRemoveSuccess) {
        await transferToken(address as IAddress, receiverAddress as IAddress, selectNft, 'nft')
      }
    }
    transfer()
  }, [isRemoveSuccess])
  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])
  return (
    <div className="" key={refreshKey}>
      <div className="h-[252px] overflow-y-auto p-1">
        {isLoading ? (
          <div className={twMerge('grid grid-cols-3 gap-3')}>
            {new Array(10)
              .fill('')
              ?.map((_, index) => <Skeleton className=" aspect-auto h-[130px] w-full rounded-2xl" key={index} />)}
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-3 md:grid-cols-3">
            {/* {tokenList?.map((nft) => (
              <NftCard
                key={nft.id}
                handleClick={() => setSelectNft(nft.id)}
                className={twMerge(selectNft === nft.id && 'border-none ring-2 ring-borderDarkGray')}
                preventDefault
                simpleCard
                {...nft}
              />
            ))} */}
            {data?.pages.map((page: { items: (React.JSX.IntrinsicAttributes & INftCard)[] }) =>
              page.items.map((nft: React.JSX.IntrinsicAttributes & INftCard) => (
                <NftCard
                  key={nft.id}
                  handleClick={() => setSelectNft(nft.id)}
                  className={twMerge(selectNft === nft.id && 'border-none ring-2 ring-borderDarkGray')}
                  preventDefault
                  simpleCard
                  {...nft}
                />
              ))
            )}
            {isFetchingNextPage && (
              <>
                {new Array(20)
                  .fill('')
                  ?.map((_, index) => <Skeleton className=" aspect-auto h-[130px] w-full rounded-2xl" key={index} />)}
              </>
            )}
            <div ref={ref}>{isFetchingNextPage ? <p /> : hasNextPage ? <p /> : <p />}</div>
          </div>
        )}
      </div>
      <div className="py-4">
        <Input
          placeholder="0x..."
          className="mt-0.5 rounded bg-buttonTertiary"
          inputClassName="text-sm text-primary font-medium text-start"
          defaultValue={receiverAddress}
          onChange={changeReceiverAddress}
        />
      </div>
      <Button
        size="lg"
        className="w-full gap-[6px] rounded-xl"
        disabled={disableTransfer}
        isLoading={isTransferLoading || isRemoveLoading || isSolanaTransferLoading}
        onClick={handleTransfer}
      >
        {t('swap_transfer_token_transfer')}
      </Button>
    </div>
  )
}

export default memo(TransferNft)
