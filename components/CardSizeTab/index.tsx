import React, { memo } from 'react'
import { TbDialpadFilled, TbLayoutGridFilled } from 'react-icons/tb'

import Tab from '../library/Tab'

interface ICardSizeTabProps {
  selectedKey?: string
  handleTab?: (key: React.Key) => void
}

const iconTabs = [
  {
    key: 'large',
    title: <TbLayoutGridFilled />,
  },
  {
    key: 'min',
    title: <TbDialpadFilled />,
  },
]

const CardSizeTab = (props: ICardSizeTabProps) => {
  const { handleTab, selectedKey } = props
  return (
    <Tab
      variants="solid"
      classNames={{
        base: 'hidden md:inline-flex',
        tabList: '!px-1 !py-1',
        tab: 'first:!pl-3 last:!pr-3 border-borderPrimary group-data-[selected=true]:border-1 group-data-[selected=true]:border-solid',
      }}
      selectedKey={selectedKey}
      list={iconTabs}
      handleTab={handleTab}
    />
  )
}

export default memo(CardSizeTab)
