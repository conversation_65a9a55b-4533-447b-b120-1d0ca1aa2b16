'use client'

import { memo, useContext, useRef } from 'react'

import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { twMerge } from 'tailwind-merge'

// import { useHover } from 'usehooks-ts'
import { BorderBeam } from '@/components/ui/border-beam'

import { PumpTokenContext } from '@/app/[locale]/(commonLayout)/pump404/PumpTokenContext'
import useLanguage from '@/hooks/useLanguage'

// import useTouchDevice from '@/hooks/useTouchDevice'
import ChainIcon from './chain-icon'
import Info from './info'

const CardRow = () => {
  const { tokenDetail } = useContext(PumpTokenContext)
  // const isTouchDevice = useTouchDevice()
  const nftRef = useRef<any>(null)
  // const defaultRef = useRef<any>(null)
  // const isHover = useHover(!isTouchDevice ? nftRef : defaultRef)
  const { lng } = useLanguage()
  const path = `/${lng}/pump404/${tokenDetail?.chainId}/${tokenDetail?.contractAddress}`
  const router = useRouter()
  const openDetail = (e: any) => {
    console.log('click nftcard')
    e.preventDefault()
    console.log('jump router', path)
    router.push(path)
  }
  return (
    <Link lang={lng} href={path} passHref legacyBehavior>
      <a
        href={path}
        onClick={openDetail}
        style={{ touchAction: 'manipulation' }}
        className={twMerge(
          'w-full rounded-md border-1 border-solid border-borderPrimary',
          'transition-transform hover:translate-y-[-2px] hover:outline hover:outline-[2px] hover:outline-borderPrimary'
        )}
      >
        <div
          className="relative flex h-[160px] w-full max-w-[416px] flex-row overflow-hidden rounded-md bg-alphaWhite100 max-md:h-[141px]"
          ref={nftRef}
        >
          <ChainIcon isHover chainId={tokenDetail?.chainId as number} className="left-3 top-2" />
          <Image
            className="h-[160px] w-[160px] rounded-l-md object-cover max-md:h-[141px] max-md:w-[141px]"
            src={tokenDetail?.logoImage as string}
            width={160}
            height={160}
            alt="king-avatar"
          />
          <div className="absolute right-0 top-0 h-1.5 w-[calc(100%-160px)] overflow-hidden bg-buttonPrimary4 max-md:w-[calc(100%-141px)]">
            <div
              className={`h-full bg-[linear-gradient(225deg,#56CCF2_0%,#0070F0_100%)]`}
              style={{ width: `${tokenDetail?.bondingCurveProgress ? Number(tokenDetail?.bondingCurveProgress) : 0}%` }}
            ></div>
          </div>
          <Info className="absolute inset-0 p-4 pl-[176px] max-md:pl-[157px]" item3Class="mb-9 max-md:mb-[17px]" />
          <BorderBeam size={160} duration={4} />
        </div>
      </a>
    </Link>
  )
}

export default memo(CardRow)
