import React, { memo, useCallback, useContext, useEffect, useMemo, useState } from 'react'
import { LuChevronLeft, LuFilter } from 'react-icons/lu'
import { useInView } from 'react-intersection-observer'

import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query'
import { useTranslate } from '@tolgee/react'
import dayjs from 'dayjs'
import { twMerge } from 'tailwind-merge'
import { useDebounceCallback, useMediaQuery } from 'usehooks-ts'
import { formatEther, parseEther } from 'viem'

import CardSizeTab from '@/components/CardSizeTab'
import EmptyComp from '@/components/EmptyComp'
import { FilterContext, IFilterContext } from '@/components/Filters/FilterContext'
import NftFilterPanel, { IFilterPanel } from '@/components/Filters/FilterPanel'
import Button from '@/components/library/Button'
import Dropdown, { IDropdownItemProps } from '@/components/library/Dropdown'
import CardListSkeleton from '@/components/library/Skeleton/CardListSkeleton'
import CardSkeleton from '@/components/library/Skeleton/CardSkeleton'
import NftCard, { INftCard } from '@/components/NftCard'

import collectionStore from '@/store/collection-store'

import { SearchOwnerNftsStr } from '@/app/[locale]/(commonLayout)/(user)/profile/[address]/components/graphql'
import { UserGraphContext } from '@/app/[locale]/(commonLayout)/(user)/profile/[address]/components/UserGraphContext'
import { UserNftQueryQueryVariables } from '@/graphql/generated'
import { GraphContext } from '@/graphql/GraphContext'
import useElectionAddress from '@/hooks/contract/address/useElectionAddress'
import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
import { deserializeAttrs, serializeAttrs, useCollectionPersistence } from '@/hooks/useCollectionPersistence'
import { isEVMAddress } from '@/hooks/useSafeAddress'
import { isSolanaChain } from '@/hooks/useSolanaInfo'
import { useReadNftSearchParams, useUpdateNftSearchParams } from '@/hooks/useUpdateSearchParams'
import { getChainInfo } from '@/libs/common/chain'
import { formatTokenId } from '@/libs/common/format'
// import { supportChainId } from '@/libs/contract/wagmiConf'
// import { ponder_userNft } from '@/libs/graph'
import { toLowerCaseLettersOnly } from '@/libs/utils/util'
import { tokenSearch as tokenSearchReq } from '@/request/api/nft'
import { IAddress } from '@/types/IAddress'
import { IChain } from '@/types/IChain'
import { INftItem } from '@/types/INftItem'

interface INftListProps {
  tokenList?: INftItem[]
  isLoading?: boolean
  filterList?: IDropdownItemProps[]
  filterPanelOptions?: IFilterPanel[]
  address: IAddress
  type: 'all' | 'sale'
}

const NftList = (props: INftListProps) => {
  const namespace = props.type === 'sale' ? 'onSale' : 'nfts'
  const updateSearchParams = useUpdateNftSearchParams(namespace)
  const readSearchParams = useReadNftSearchParams(namespace)
  const { getChainCurrency } = getChainInfo()
  const urlParams = readSearchParams()
  const queryClient = useQueryClient()
  const { ref, inView } = useInView({
    threshold: 1, // Trigger when fully visible
  })
  const { filterList, filterPanelOptions, address, type } = props
  const { ownerContracts, allPumpContracts: pumpContracts } = useContext(UserGraphContext)
  const collections = collectionStore((state) => state.collections)
  const { saveCollectionToStorage, loadCollectionFromStorage, clearCollectionStorage } =
    useCollectionPersistence(namespace)
  const fusion = useFusionAddress()
  const defaultVariables: UserNftQueryQueryVariables = useMemo(() => {
    return type === 'all'
      ? {
          orderBy: 'createTime',
          orderDirection: 'desc',
          ...urlParams,
        }
      : {
          orderBy: 'createTime',
          orderDirection: 'desc',
          price_not: 0,
          priceEndTime_gt: dayjs().unix(),
          ...urlParams,
        }
  }, [type])
  const election = useElectionAddress()
  const { t } = useTranslate(['common', 'profile'])
  const isPhone = useMediaQuery('(max-width: 768px)')
  const [isSmallCard, setIsSmallCard] = useState<boolean>(urlParams.isSmall || false)
  const [selectedDropdown, setSelectedDropdown] = useState<string>(urlParams.selectedDropdown || 'received')
  const [isFilter, setIsFilter] = useState<boolean>(false)
  const [filterParams, setFilterParams] = useState<any>(() => {
    const initialFilterParams: any = { status: 'all', chainId: 'all' }
    if (urlParams.chainId) {
      initialFilterParams.chainId = urlParams.chainId.toString()
    }
    if (urlParams.price_not === '0' && urlParams.priceEndTime_gt) {
      initialFilterParams.status = 'buy'
    }
    if (urlParams.priceEndTime_lt) {
      initialFilterParams.status = 'noSale'
    }
    if (namespace === 'onSale') {
      initialFilterParams.status = 'buy'
    }
    if (urlParams.price_lte || urlParams.price_gte) {
      initialFilterParams.price = {
        minValue: urlParams.price_gte ? formatEther(BigInt(urlParams.price_gte)) : '',
        maxValue:
          urlParams.price_lte && initialFilterParams.status !== 'noSale'
            ? formatEther(BigInt(urlParams.price_lte))
            : '',
        symbol: getChainCurrency(urlParams.chainId || 8921) || 'ALG',
      }
    }
    // Restore collection from URL and localStorage
    if (urlParams.collectionAddress && urlParams.collectionChainId) {
      const collectionData = loadCollectionFromStorage(urlParams.collectionAddress, urlParams.collectionChainId)
      if (collectionData) {
        initialFilterParams.collection = collectionData.collection
        if (urlParams.attrs) {
          initialFilterParams.attrs = deserializeAttrs(urlParams.attrs)
        } else {
          initialFilterParams.attrs = collectionData.attrs || []
        }
      }
    }

    return initialFilterParams
  })
  const [filterState, setFilterState] = useState({
    status: null,
    price: {
      minValue: null,
      maxValue: null,
    },
  })
  const filterContext: IFilterContext = {
    refreshNftSize: setIsSmallCard,
    smallNftCard: isSmallCard,
    changeFilter: setIsFilter,
    isFilter,
    changeFilterParams: setFilterParams,
    filterParams,
  }
  const [variables, setVariables] = useState<UserNftQueryQueryVariables>(defaultVariables)
  const [attrs, setAttrs] = useState<any[]>(() => {
    const attrData = (urlParams.attrs && deserializeAttrs(urlParams.attrs)) || []
    return attrData
  })
  const isPump = useCallback(
    (contract: IAddress | string) => {
      return pumpContracts?.includes(contract)
    },
    [pumpContracts]
  )
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, refetch }: any = useInfiniteQuery({
    queryKey: ['getOwnerGraphNfts', address, type, variables],
    queryFn: async ({ pageParam = null }) => {
      const isSolana = isSolanaChain(variables.chainId as unknown as IChain) || !isEVMAddress(address)
      const params: any = {
        query: SearchOwnerNftsStr,
        variables: {
          ...variables,
          after: pageParam || null,
          limit: 20,
          contractAddress_in: variables.contractAddress
            ? [variables.contractAddress as IAddress | string]
            : ownerContracts,
          ownerId: isSolana ? address?.toString() : (toLowerCaseLettersOnly(address as IAddress) as IAddress | string),
          platformNot: 'LIKN',
        },
        operationName: 'UserNftQuery',
        attrs: attrs,
        contractAddress: isSolana ? null : (toLowerCaseLettersOnly(fusion) as IAddress),
      }
      if (!params.attrs || params.attrs.length === 0) {
        delete params.attrs
      }
      if (params.attrs && params.attrs.length > 0) {
        params.contractAddress = isSolana
          ? filterParams.collection?.contractAddress?.toString()
          : toLowerCaseLettersOnly(filterParams.collection?.contractAddress)
      }
      const { data } = await tokenSearchReq({ ...params })
      const { tokens, pageInfo } = data

      const newItems = tokens.map((item: any) => {
        const { attributes = [] } = item
        let level = item?.level || ''
        attributes?.map((item: { trait_type: string; value: any }) => {
          if (item?.trait_type == 'Level') {
            level = item?.value
          }
        })
        return {
          cover: item?.image || '',
          collectionName: isPump(item?.contractAddress)
            ? `${item?.name?.split('#')?.[0]}`
            : collections?.find(
                (cItem) => toLowerCaseLettersOnly(cItem.address) === toLowerCaseLettersOnly(item.contractAddress)
              )?.name,
          name: isPump(item.contractAddress)
            ? `${item?.name}`
            : toLowerCaseLettersOnly(item.contractAddress) === toLowerCaseLettersOnly(election)
              ? `${item?.name} #${formatTokenId(item.tokenId)}`
              : `Rabbit #${formatTokenId(item.tokenId)}`,
          // name: `Rabbit #${item.tokenId.toString().slice(0, 5)}`,
          id: item.tokenId,
          collectionAddress: item.contractAddress,
          price: 0.1,
          highestBid: 0,
          url: item.tokenURI,
          iframe_url: item?.iframe_url || '',
          isLocked: item?.isLocked,
          nftDeatil: {
            ownerAddress: item?.ownerId,
            price: item?.price,
            priceEndTime: item?.priceEndTime,
            priceStartTime: item?.priceStartTime,
            offers: item?.offers?.items || [],
            chainId: item?.chainId,
          },
          level: level ? (level?.toString()?.startsWith('Lv') ? level : `Lv-${level}`) : '',
        } as INftCard
      })
      return { items: newItems, pageInfo }
    },
    initialPageParam: null,
    getNextPageParam: (lastPage) => lastPage?.pageInfo?.endCursor || null,
    enabled: !!address && !!(ownerContracts?.length > 0),
  })
  useEffect(() => {
    const urlUpdateParams: any = {
      ...variables,
      isSmall: isSmallCard,
      type: namespace,
      // isFilter: isFitlter,
      collectionAddress: undefined,
      collectionChainId: undefined,
      attrs: undefined,
    }

    // Add collection-related parameters to URL
    if (filterParams.collection) {
      urlUpdateParams.collectionAddress = filterParams.collection.address
      urlUpdateParams.collectionChainId = filterParams.collection.chainId.toString()

      // Save collection to localStorage
      saveCollectionToStorage(filterParams.collection, filterParams.attrs || [])

      // Serialize attrs for URL if they exist
      if (filterParams.attrs && filterParams.attrs.length > 0) {
        urlUpdateParams.attrs = serializeAttrs(filterParams.attrs)
      }
    }

    updateSearchParams(urlUpdateParams)
  }, [variables, isSmallCard, filterParams, saveCollectionToStorage, namespace])
  const filterLength = useMemo(() => {
    return (
      Object.keys(filterParams).filter((key) => {
        if (key === 'attrs' && filterParams[key]?.length === 0) {
          return false
        }
        if (key === 'status' && filterParams[key] === 'buy' && namespace === 'onSale') {
          return false
        }
        return filterParams[key] !== null && filterParams[key] !== undefined && filterParams[key] !== 'all'
      }).length || 0
    )
  }, [filterParams, namespace])
  const clearAll = () => {
    setFilterParams?.({})
    setFilterState({
      status: null,
      price: {
        minValue: null,
        maxValue: null,
      },
    })
    setAttrs([])
    delete variables.priceEndTime_gt
    delete variables.price_gte
    delete variables.priceEndTime_lt
    delete variables.price_lte
    if (type === 'all') {
      delete variables.price_not
    }
    if (type === 'sale') {
      variables.price_not = 0
      variables.priceEndTime_gt = dayjs().unix()
    }
    setVariables({
      ...variables,
    })
    clearCollectionStorage()
  }
  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage])
  useEffect(() => {
    console.log('params', filterParams)
  }, [filterParams])
  const filterDropdownList: IDropdownItemProps[] = useMemo(() => {
    if (filterList) return filterList
    return [
      {
        key: 'received',
        value: t('received', { ns: 'common' }),
      },
      {
        key: 'recently',
        value: t('listed', { ns: 'common' }),
      },
      {
        key: 'low',
        value: t('low', { ns: 'common' }),
      },
      {
        key: 'high',
        value: t('high', { ns: 'common' }),
      },
    ]
  }, [filterList, t])
  const onDropDownAction = (key: any) => {
    // if (type === 'all') {
    //   delete variables.price_not
    //   delete variables.priceEndTime_gt
    // }
    switch (key) {
      case 'received':
        setVariables({
          ...variables,
          orderBy: 'createTime',
          orderDirection: 'desc',
        })
        break
      case 'recently':
        setVariables({
          ...variables,
          orderBy: 'priceStartTime',
          orderDirection: 'desc',
          // price_not: 0,
          // priceEndTime_gt: dayjs().unix(),
        })
        break
      case 'low':
        setVariables({
          ...variables,
          orderBy: 'price',
          orderDirection: 'asc',
          // priceEndTime_gt: dayjs().unix(),
          // price_not: 0,
        })
        break
      case 'high':
        setVariables({
          ...variables,
          // price_not: 0,
          orderBy: 'price',
          orderDirection: 'desc',
          // priceEndTime_gt: dayjs().unix(),
        })
        break
      default:
        setVariables({
          ...variables,
          orderBy: 'createTime',
          orderDirection: 'desc',
        })
    }
    setSelectedDropdown(key)
  }
  const applyFilters = (filters: any) => {
    const newVariables = { ...variables }
    const currentTimestampInSeconds = dayjs().unix()

    delete newVariables.priceEndTime_gt
    delete newVariables.priceEndTime_lt
    delete newVariables.price_gte
    delete newVariables.price_lte
    delete newVariables.price_not

    if (filters.status) {
      if (filters.status === 'all') {
      } else if (filters.status === 'buy') {
        newVariables.priceEndTime_gt = currentTimestampInSeconds
        newVariables.price_not = 0
      } else if (filters.status === 'noSale') {
        newVariables.priceEndTime_lt = currentTimestampInSeconds
      }
    }

    if (filters.price) {
      if (filters.price.minValue) {
        newVariables.price_gte = Number(parseEther(filters.price.minValue).toString())
        newVariables.priceEndTime_gt = currentTimestampInSeconds
      }

      if (filters.price.maxValue) {
        newVariables.price_lte = Number(parseEther(filters.price.maxValue).toString())
        if (!newVariables.priceEndTime_gt) {
          newVariables.priceEndTime_gt = currentTimestampInSeconds
        }
      }
    }

    setVariables(newVariables)
  }
  useEffect(() => {
    if (variables) {
      queryClient.invalidateQueries({ queryKey: ['getOwnerGraphNfts', address, type, variables, isSmallCard] })
    }
  }, [variables])
  useEffect(() => {
    const updatedVariables = { ...variables }

    // 处理 chainId 过滤
    if (filterParams?.chainId) {
      if (filterParams.chainId === 'all') {
        delete updatedVariables.chainId
      } else {
        updatedVariables.chainId = isSolanaChain(filterParams.chainId)
          ? filterParams.chainId.toString()
          : Number(filterParams.chainId)
      }
    } else {
      delete updatedVariables.chainId
    }

    // 处理 collection 过滤
    if (filterParams?.collection) {
      updatedVariables.contractAddress = isEVMAddress(filterParams.collection.address)
        ? (toLowerCaseLettersOnly(filterParams.collection.address) as IAddress)
        : filterParams.collection.address.toString()
    } else {
      delete updatedVariables.contractAddress
    }

    // 更新状态
    setVariables(updatedVariables)

    // 处理属性过滤
    const shouldUpdateAttrs = filterParams?.collection && filterParams?.attrs?.length > 0
    changeAttrs(shouldUpdateAttrs ? filterParams.attrs : [])
  }, [filterParams?.chainId, filterParams?.collection, filterParams?.attrs])
  useEffect(() => {
    if (filterParams?.status && type === 'all') {
      setFilterState((prev) => ({
        ...prev,
        status: filterParams.status,
      }))

      applyFilters({
        ...filterState,
        status: filterParams.status,
      })
    }
  }, [filterParams?.status, type])
  useEffect(() => {
    if (filterParams?.price) {
      // if (filterParams?.price?.minValue) {
      //   delete variables.price_gte
      //   delete variables.priceEndTime_gt
      //   const currentTimestampInSeconds = dayjs().unix()
      //   variables.price_gte = Number(parseEther(filterParams.price.minValue).toString())
      //   variables.priceEndTime_gt = currentTimestampInSeconds
      // }
      // if (!filterParams?.price?.minValue) {
      //   delete variables.price_gte
      // }
      // if (!filterParams?.price?.maxValue) {
      //   delete variables.price_lte
      // }
      // if (filterParams?.price?.maxValue) {
      //   delete variables.price_lte
      //   delete variables.priceEndTime_gt
      //   const currentTimestampInSeconds = dayjs().unix()
      //   variables.price_lte = Number(parseEther(filterParams.price.maxValue).toString())
      //   variables.priceEndTime_gt = currentTimestampInSeconds
      // }
      // if (!filterParams?.price?.minValue && !filterParams?.price?.maxValue && variables.price_not !== 0) {
      //   delete variables.priceEndTime_gt
      // }
      // setVariables({
      //   ...variables,
      // })
      setFilterState((prev) => ({
        ...prev,
        price: {
          minValue: filterParams.price?.minValue || null,
          maxValue: filterParams.price?.maxValue || null,
        },
      }))

      applyFilters({
        ...filterState,
        price: {
          minValue: filterParams.price?.minValue || null,
          maxValue: filterParams.price?.maxValue || null,
        },
      })
    }
  }, [filterParams?.price])
  const changeAttrs = useDebounceCallback((arr) => {
    setAttrs(arr)
  }, 200)
  useEffect(() => {
    if (attrs) {
      queryClient.invalidateQueries({ queryKey: ['getOwnerGraphNfts', address, type, variables] })
    }
  }, [attrs])
  useEffect(() => {
    console.log('filterParams', filterParams)
  }, [filterParams])
  return (
    <GraphContext.Provider
      value={{
        refetchGraph: refetch,
      }}
    >
      <FilterContext.Provider value={filterContext}>
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-4  md:justify-between">
            <div className="flex justify-start gap-4">
              <Button
                color="gray"
                onClick={() => setIsFilter(!isFilter)}
                isLoading={isLoading}
                badge={filterLength}
                className=" hidden gap-1.5 lg:flex"
                badgeClassName="hidden lg:flex"
              >
                {isFilter ? <LuChevronLeft /> : <LuFilter />}
                <span>{t('common_filter', { ns: 'common' })}</span>
              </Button>
              {filterLength > 0 && (
                <Button color="gray" className="hidden  gap-1.5 lg:flex" onClick={clearAll}>
                  <span className="text-sm font-semibold">{t('clear_all', { ns: 'filters' })}</span>
                </Button>
              )}
            </div>
            <div className="flex items-center gap-4">
              <Dropdown list={filterDropdownList} onAction={onDropDownAction} defaultValue={selectedDropdown} />
              <CardSizeTab
                handleTab={(key) => setIsSmallCard(key === 'min')}
                selectedKey={isSmallCard ? 'min' : 'large'}
              />
            </div>
          </div>
          {isLoading && (data?.pages?.[0]?.items === null || data?.pages?.[0]?.items === undefined) && (
            <CardListSkeleton />
          )}
          <div className="flex flex-row gap-4">
            {isFilter && (
              <div className="lg:sticky">
                <NftFilterPanel
                  onClose={() => setIsFilter(false)}
                  type={namespace}
                  className="mt-0"
                  options={filterPanelOptions}
                />
              </div>
            )}
            {!isLoading && data?.pages?.[0]?.items.length === 0 && <EmptyComp />}
            {!isLoading && isPhone ? (
              <div
                className={twMerge(
                  'grid h-fit w-full grid-cols-2 gap-3',
                  data?.pages?.[0]?.items?.length === 0 && 'hidden'
                )}
              >
                {/* {tokenList?.map((nft) => <NftCard key={`${nft?.id}_${nft?.collectionAddress}`} {...nft} />)} */}
                {data?.pages.map((page: { items: (React.JSX.IntrinsicAttributes & INftCard)[] }) =>
                  page.items.map((nft: React.JSX.IntrinsicAttributes & INftCard) => (
                    <NftCard key={`${nft?.id}_${nft?.collectionAddress}`} {...nft} />
                  ))
                )}
                {isFetchingNextPage && <>{new Array(20).fill('')?.map((_, index) => <CardSkeleton key={index} />)}</>}
                <div ref={ref}>{isFetchingNextPage ? <p /> : hasNextPage ? <p /> : <p />}</div>
              </div>
            ) : (
              <div className={twMerge('w-full ', data?.pages?.[0]?.items?.length === 0 && 'hidden')}>
                <div
                  className={twMerge('grid h-fit w-full gap-3', data?.pages?.[0]?.items?.length === 0 && 'hidden')}
                  style={{ gridTemplateColumns: `repeat(auto-fill, minmax(${isSmallCard ? '190px' : '260px'}, 1fr))` }}
                >
                  {/* {tokenList?.map((nft) => <NftCard key={`${nft?.id}_${nft?.collectionAddress}`} {...nft} isSmall={isSmallCard} />)} */}
                  {data?.pages.map((page: { items: (React.JSX.IntrinsicAttributes & INftCard)[] }) =>
                    page.items.map((nft: React.JSX.IntrinsicAttributes & INftCard) => (
                      <NftCard key={`${nft?.id}_${nft?.collectionAddress}`} {...nft} isSmall={isSmallCard} />
                    ))
                  )}
                  {isFetchingNextPage && (
                    <>{new Array(20).fill('')?.map((_, index) => <CardSkeleton key={index} isSmall={isSmallCard} />)}</>
                  )}
                  <div ref={ref}>{isFetchingNextPage ? <p /> : hasNextPage ? <p /> : <p />}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </FilterContext.Provider>
    </GraphContext.Provider>
  )
}

export default memo(NftList)
